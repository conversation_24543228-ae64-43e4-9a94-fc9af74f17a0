# Acu-RTMPOSE数据预处理Pipeline配置
# 针对不同训练阶段和应用场景的优化pipeline

# 基础配置
input_size = (256, 192)
simcc_split_ratio = 2.0
sigma = (4.9, 5.66)

# ============================================================================
# 1. 强化训练Pipeline - 用于模型初期训练，数据增强较强
# ============================================================================
strong_train_pipeline = [
    # 基础加载
    dict(type='LoadImage'),
    dict(type='GetBBoxCenterScale', padding=1.3),  # 稍大的padding
    dict(type='AcuRTMPoseKeypointSeparator'),
    
    # 强数据增强
    dict(type='AcuRTMPoseRandomFlip', prob=0.6),
    dict(type='RandomHalfBody', prob=0.4),
    dict(
        type='RandomBBoxTransform',
        scale_factor=[0.6, 1.4],
        rotate_factor=80,
        shift_factor=0.25),
    dict(type='TopdownAffine', input_size=input_size),
    
    # 颜色和噪声增强
    dict(type='mmdet.YOLOXHSVRandomAug'),
    dict(
        type='Albumentation',
        transforms=[
            dict(type='Blur', blur_limit=5, p=0.15),
            dict(type='MedianBlur', blur_limit=5, p=0.15),
            dict(type='GaussNoise', var_limit=(10.0, 80.0), p=0.25),
            dict(type='RandomBrightnessContrast', 
                 brightness_limit=0.3, contrast_limit=0.3, p=0.4),
            dict(
                type='CoarseDropout',
                max_holes=2,
                max_height=0.4,
                max_width=0.4,
                min_holes=1,
                min_height=0.15,
                min_width=0.15,
                p=0.5),
        ]),
    
    # 目标生成和打包
    dict(
        type='AcuRTMPoseGenerateTarget',
        input_size=input_size,
        simcc_split_ratio=simcc_split_ratio,
        sigma=sigma),
    dict(type='AcuRTMPosePackInputs')
]

# ============================================================================
# 2. 标准训练Pipeline - 用于常规训练，平衡增强和精度
# ============================================================================
standard_train_pipeline = [
    # 基础加载
    dict(type='LoadImage'),
    dict(type='GetBBoxCenterScale', padding=1.25),
    dict(type='AcuRTMPoseKeypointSeparator'),
    
    # 适度数据增强
    dict(type='AcuRTMPoseRandomFlip', prob=0.5),
    dict(type='RandomHalfBody', prob=0.2),
    dict(
        type='RandomBBoxTransform',
        scale_factor=[0.7, 1.3],
        rotate_factor=60,
        shift_factor=0.15),
    dict(type='TopdownAffine', input_size=input_size),
    
    # 轻度颜色增强
    dict(type='mmdet.YOLOXHSVRandomAug'),
    dict(
        type='Albumentation',
        transforms=[
            dict(type='Blur', blur_limit=3, p=0.1),
            dict(type='MedianBlur', blur_limit=3, p=0.1),
            dict(type='GaussNoise', var_limit=(10.0, 50.0), p=0.2),
            dict(type='RandomBrightnessContrast', 
                 brightness_limit=0.2, contrast_limit=0.2, p=0.3),
            dict(
                type='CoarseDropout',
                max_holes=1,
                max_height=0.3,
                max_width=0.3,
                min_holes=1,
                min_height=0.1,
                min_width=0.1,
                p=0.3),
        ]),
    
    # 目标生成和打包
    dict(
        type='AcuRTMPoseGenerateTarget',
        input_size=input_size,
        simcc_split_ratio=simcc_split_ratio,
        sigma=sigma),
    dict(type='AcuRTMPosePackInputs')
]

# ============================================================================
# 3. 精细调优Pipeline - 用于模型后期微调，数据增强较轻
# ============================================================================
finetune_train_pipeline = [
    # 基础加载
    dict(type='LoadImage'),
    dict(type='GetBBoxCenterScale', padding=1.2),
    dict(type='AcuRTMPoseKeypointSeparator'),
    
    # 轻度数据增强
    dict(type='AcuRTMPoseRandomFlip', prob=0.4),
    dict(type='RandomHalfBody', prob=0.1),
    dict(
        type='RandomBBoxTransform',
        scale_factor=[0.8, 1.2],
        rotate_factor=30,
        shift_factor=0.1),
    dict(type='TopdownAffine', input_size=input_size),
    
    # 最小颜色增强
    dict(
        type='Albumentation',
        transforms=[
            dict(type='Blur', blur_limit=2, p=0.05),
            dict(type='GaussNoise', var_limit=(5.0, 25.0), p=0.1),
            dict(type='RandomBrightnessContrast', 
                 brightness_limit=0.1, contrast_limit=0.1, p=0.2),
        ]),
    
    # 目标生成和打包
    dict(
        type='AcuRTMPoseGenerateTarget',
        input_size=input_size,
        simcc_split_ratio=simcc_split_ratio,
        sigma=sigma),
    dict(type='AcuRTMPosePackInputs')
]

# ============================================================================
# 4. 验证Pipeline - 用于验证和测试
# ============================================================================
val_pipeline = [
    dict(type='LoadImage'),
    dict(type='GetBBoxCenterScale', padding=1.25),
    dict(type='AcuRTMPoseKeypointSeparator'),
    dict(type='TopdownAffine', input_size=input_size),
    dict(
        type='AcuRTMPoseGenerateTarget',
        input_size=input_size,
        simcc_split_ratio=simcc_split_ratio,
        sigma=sigma),
    dict(type='AcuRTMPosePackInputs')
]

# ============================================================================
# 5. 推理Pipeline - 用于实际部署推理
# ============================================================================
inference_pipeline = [
    dict(type='LoadImage'),
    dict(type='GetBBoxCenterScale', padding=1.25),
    dict(type='TopdownAffine', input_size=input_size),
    dict(type='PackPoseInputs')
]

# ============================================================================
# 6. TTA (Test Time Augmentation) Pipeline - 用于测试时增强
# ============================================================================
tta_pipeline = [
    dict(type='LoadImage'),
    dict(type='GetBBoxCenterScale', padding=1.25),
    
    # 多尺度测试
    dict(
        type='TestTimeAug',
        transforms=[
            # 原始尺度
            [dict(type='TopdownAffine', input_size=input_size)],
            # 稍大尺度
            [dict(type='TopdownAffine', input_size=(288, 216))],
            # 翻转测试
            [
                dict(type='TopdownAffine', input_size=input_size),
                dict(type='RandomFlip', prob=1.0, direction='horizontal')
            ],
        ]),
    dict(type='PackPoseInputs')
]

# ============================================================================
# Pipeline选择器 - 根据训练阶段选择合适的pipeline
# ============================================================================
def get_pipeline(stage='standard'):
    """根据训练阶段获取对应的pipeline
    
    Args:
        stage (str): 训练阶段
            - 'strong': 强化训练阶段
            - 'standard': 标准训练阶段  
            - 'finetune': 精细调优阶段
            - 'val': 验证阶段
            - 'inference': 推理阶段
            - 'tta': 测试时增强
    
    Returns:
        list: 对应的pipeline配置
    """
    pipelines = {
        'strong': strong_train_pipeline,
        'standard': standard_train_pipeline,
        'finetune': finetune_train_pipeline,
        'val': val_pipeline,
        'inference': inference_pipeline,
        'tta': tta_pipeline,
    }
    
    if stage not in pipelines:
        raise ValueError(f"Unknown pipeline stage: {stage}. "
                        f"Available stages: {list(pipelines.keys())}")
    
    return pipelines[stage]

# 默认导出标准pipeline
train_pipeline = standard_train_pipeline
test_pipeline = inference_pipeline
