dataset_info = dict(
    dataset_name='300w',
    paper_info=dict(
        author='<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, Epameinondas '
        'and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Georg<PERSON> and <PERSON>afei<PERSON>, <PERSON><PERSON> '
        'and <PERSON><PERSON>, <PERSON><PERSON>',
        title='300 faces in-the-wild challenge: '
        'Database and results',
        container='Image and vision computing',
        year='2016',
        homepage='https://ibug.doc.ic.ac.uk/resources/300-W/',
    ),
    keypoint_info={
        0: dict(name='kpt-0', id=0, color=[255, 0, 0], type='', swap='kpt-16'),
        1: dict(name='kpt-1', id=1, color=[255, 0, 0], type='', swap='kpt-15'),
        2: dict(name='kpt-2', id=2, color=[255, 0, 0], type='', swap='kpt-14'),
        3: dict(name='kpt-3', id=3, color=[255, 0, 0], type='', swap='kpt-13'),
        4: dict(name='kpt-4', id=4, color=[255, 0, 0], type='', swap='kpt-12'),
        5: dict(name='kpt-5', id=5, color=[255, 0, 0], type='', swap='kpt-11'),
        6: dict(name='kpt-6', id=6, color=[255, 0, 0], type='', swap='kpt-10'),
        7: dict(name='kpt-7', id=7, color=[255, 0, 0], type='', swap='kpt-9'),
        8: dict(name='kpt-8', id=8, color=[255, 0, 0], type='', swap=''),
        9: dict(name='kpt-9', id=9, color=[255, 0, 0], type='', swap='kpt-7'),
        10:
        dict(name='kpt-10', id=10, color=[255, 0, 0], type='', swap='kpt-6'),
        11:
        dict(name='kpt-11', id=11, color=[255, 0, 0], type='', swap='kpt-5'),
        12:
        dict(name='kpt-12', id=12, color=[255, 0, 0], type='', swap='kpt-4'),
        13:
        dict(name='kpt-13', id=13, color=[255, 0, 0], type='', swap='kpt-3'),
        14:
        dict(name='kpt-14', id=14, color=[255, 0, 0], type='', swap='kpt-2'),
        15:
        dict(name='kpt-15', id=15, color=[255, 0, 0], type='', swap='kpt-1'),
        16:
        dict(name='kpt-16', id=16, color=[255, 0, 0], type='', swap='kpt-0'),
        17:
        dict(name='kpt-17', id=17, color=[255, 0, 0], type='', swap='kpt-26'),
        18:
        dict(name='kpt-18', id=18, color=[255, 0, 0], type='', swap='kpt-25'),
        19:
        dict(name='kpt-19', id=19, color=[255, 0, 0], type='', swap='kpt-24'),
        20:
        dict(name='kpt-20', id=20, color=[255, 0, 0], type='', swap='kpt-23'),
        21:
        dict(name='kpt-21', id=21, color=[255, 0, 0], type='', swap='kpt-22'),
        22:
        dict(name='kpt-22', id=22, color=[255, 0, 0], type='', swap='kpt-21'),
        23:
        dict(name='kpt-23', id=23, color=[255, 0, 0], type='', swap='kpt-20'),
        24:
        dict(name='kpt-24', id=24, color=[255, 0, 0], type='', swap='kpt-19'),
        25:
        dict(name='kpt-25', id=25, color=[255, 0, 0], type='', swap='kpt-18'),
        26:
        dict(name='kpt-26', id=26, color=[255, 0, 0], type='', swap='kpt-17'),
        27: dict(name='kpt-27', id=27, color=[255, 0, 0], type='', swap=''),
        28: dict(name='kpt-28', id=28, color=[255, 0, 0], type='', swap=''),
        29: dict(name='kpt-29', id=29, color=[255, 0, 0], type='', swap=''),
        30: dict(name='kpt-30', id=30, color=[255, 0, 0], type='', swap=''),
        31:
        dict(name='kpt-31', id=31, color=[255, 0, 0], type='', swap='kpt-35'),
        32:
        dict(name='kpt-32', id=32, color=[255, 0, 0], type='', swap='kpt-34'),
        33: dict(name='kpt-33', id=33, color=[255, 0, 0], type='', swap=''),
        34:
        dict(name='kpt-34', id=34, color=[255, 0, 0], type='', swap='kpt-32'),
        35:
        dict(name='kpt-35', id=35, color=[255, 0, 0], type='', swap='kpt-31'),
        36:
        dict(name='kpt-36', id=36, color=[255, 0, 0], type='', swap='kpt-45'),
        37:
        dict(name='kpt-37', id=37, color=[255, 0, 0], type='', swap='kpt-44'),
        38:
        dict(name='kpt-38', id=38, color=[255, 0, 0], type='', swap='kpt-43'),
        39:
        dict(name='kpt-39', id=39, color=[255, 0, 0], type='', swap='kpt-42'),
        40:
        dict(name='kpt-40', id=40, color=[255, 0, 0], type='', swap='kpt-47'),
        41: dict(
            name='kpt-41', id=41, color=[255, 0, 0], type='', swap='kpt-46'),
        42: dict(
            name='kpt-42', id=42, color=[255, 0, 0], type='', swap='kpt-39'),
        43: dict(
            name='kpt-43', id=43, color=[255, 0, 0], type='', swap='kpt-38'),
        44: dict(
            name='kpt-44', id=44, color=[255, 0, 0], type='', swap='kpt-37'),
        45: dict(
            name='kpt-45', id=45, color=[255, 0, 0], type='', swap='kpt-36'),
        46: dict(
            name='kpt-46', id=46, color=[255, 0, 0], type='', swap='kpt-41'),
        47: dict(
            name='kpt-47', id=47, color=[255, 0, 0], type='', swap='kpt-40'),
        48: dict(
            name='kpt-48', id=48, color=[255, 0, 0], type='', swap='kpt-54'),
        49: dict(
            name='kpt-49', id=49, color=[255, 0, 0], type='', swap='kpt-53'),
        50: dict(
            name='kpt-50', id=50, color=[255, 0, 0], type='', swap='kpt-52'),
        51: dict(name='kpt-51', id=51, color=[255, 0, 0], type='', swap=''),
        52: dict(
            name='kpt-52', id=52, color=[255, 0, 0], type='', swap='kpt-50'),
        53: dict(
            name='kpt-53', id=53, color=[255, 0, 0], type='', swap='kpt-49'),
        54: dict(
            name='kpt-54', id=54, color=[255, 0, 0], type='', swap='kpt-48'),
        55: dict(
            name='kpt-55', id=55, color=[255, 0, 0], type='', swap='kpt-59'),
        56: dict(
            name='kpt-56', id=56, color=[255, 0, 0], type='', swap='kpt-58'),
        57: dict(name='kpt-57', id=57, color=[255, 0, 0], type='', swap=''),
        58: dict(
            name='kpt-58', id=58, color=[255, 0, 0], type='', swap='kpt-56'),
        59: dict(
            name='kpt-59', id=59, color=[255, 0, 0], type='', swap='kpt-55'),
        60: dict(
            name='kpt-60', id=60, color=[255, 0, 0], type='', swap='kpt-64'),
        61: dict(
            name='kpt-61', id=61, color=[255, 0, 0], type='', swap='kpt-63'),
        62: dict(name='kpt-62', id=62, color=[255, 0, 0], type='', swap=''),
        63: dict(
            name='kpt-63', id=63, color=[255, 0, 0], type='', swap='kpt-61'),
        64: dict(
            name='kpt-64', id=64, color=[255, 0, 0], type='', swap='kpt-60'),
        65: dict(
            name='kpt-65', id=65, color=[255, 0, 0], type='', swap='kpt-67'),
        66: dict(name='kpt-66', id=66, color=[255, 0, 0], type='', swap=''),
        67: dict(
            name='kpt-67', id=67, color=[255, 0, 0], type='', swap='kpt-65'),
    },
    skeleton_info={},
    joint_weights=[1.] * 68,
    sigmas=[])
