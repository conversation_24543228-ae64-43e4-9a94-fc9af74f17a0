dataset_info = dict(
    dataset_name='interhand3d',
    paper_info=dict(
        author='<PERSON>, Gyeongsik and <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON>, He and '
        'Shirator<PERSON>, <PERSON>ka<PERSON> and Lee, Kyoung Mu',
        title='InterHand2.6M: A dataset and baseline for 3D '
        'interacting hand pose estimation from a single RGB image',
        container='arXiv',
        year='2020',
        homepage='https://mks0601.github.io/InterHand2.6M/',
    ),
    keypoint_info={
        0:
        dict(
            name='right_thumb4',
            id=0,
            color=[255, 128, 0],
            type='',
            swap='left_thumb4'),
        1:
        dict(
            name='right_thumb3',
            id=1,
            color=[255, 128, 0],
            type='',
            swap='left_thumb3'),
        2:
        dict(
            name='right_thumb2',
            id=2,
            color=[255, 128, 0],
            type='',
            swap='left_thumb2'),
        3:
        dict(
            name='right_thumb1',
            id=3,
            color=[255, 128, 0],
            type='',
            swap='left_thumb1'),
        4:
        dict(
            name='right_forefinger4',
            id=4,
            color=[255, 153, 255],
            type='',
            swap='left_forefinger4'),
        5:
        dict(
            name='right_forefinger3',
            id=5,
            color=[255, 153, 255],
            type='',
            swap='left_forefinger3'),
        6:
        dict(
            name='right_forefinger2',
            id=6,
            color=[255, 153, 255],
            type='',
            swap='left_forefinger2'),
        7:
        dict(
            name='right_forefinger1',
            id=7,
            color=[255, 153, 255],
            type='',
            swap='left_forefinger1'),
        8:
        dict(
            name='right_middle_finger4',
            id=8,
            color=[102, 178, 255],
            type='',
            swap='left_middle_finger4'),
        9:
        dict(
            name='right_middle_finger3',
            id=9,
            color=[102, 178, 255],
            type='',
            swap='left_middle_finger3'),
        10:
        dict(
            name='right_middle_finger2',
            id=10,
            color=[102, 178, 255],
            type='',
            swap='left_middle_finger2'),
        11:
        dict(
            name='right_middle_finger1',
            id=11,
            color=[102, 178, 255],
            type='',
            swap='left_middle_finger1'),
        12:
        dict(
            name='right_ring_finger4',
            id=12,
            color=[255, 51, 51],
            type='',
            swap='left_ring_finger4'),
        13:
        dict(
            name='right_ring_finger3',
            id=13,
            color=[255, 51, 51],
            type='',
            swap='left_ring_finger3'),
        14:
        dict(
            name='right_ring_finger2',
            id=14,
            color=[255, 51, 51],
            type='',
            swap='left_ring_finger2'),
        15:
        dict(
            name='right_ring_finger1',
            id=15,
            color=[255, 51, 51],
            type='',
            swap='left_ring_finger1'),
        16:
        dict(
            name='right_pinky_finger4',
            id=16,
            color=[0, 255, 0],
            type='',
            swap='left_pinky_finger4'),
        17:
        dict(
            name='right_pinky_finger3',
            id=17,
            color=[0, 255, 0],
            type='',
            swap='left_pinky_finger3'),
        18:
        dict(
            name='right_pinky_finger2',
            id=18,
            color=[0, 255, 0],
            type='',
            swap='left_pinky_finger2'),
        19:
        dict(
            name='right_pinky_finger1',
            id=19,
            color=[0, 255, 0],
            type='',
            swap='left_pinky_finger1'),
        20:
        dict(
            name='right_wrist',
            id=20,
            color=[255, 255, 255],
            type='',
            swap='left_wrist'),
        21:
        dict(
            name='left_thumb4',
            id=21,
            color=[255, 128, 0],
            type='',
            swap='right_thumb4'),
        22:
        dict(
            name='left_thumb3',
            id=22,
            color=[255, 128, 0],
            type='',
            swap='right_thumb3'),
        23:
        dict(
            name='left_thumb2',
            id=23,
            color=[255, 128, 0],
            type='',
            swap='right_thumb2'),
        24:
        dict(
            name='left_thumb1',
            id=24,
            color=[255, 128, 0],
            type='',
            swap='right_thumb1'),
        25:
        dict(
            name='left_forefinger4',
            id=25,
            color=[255, 153, 255],
            type='',
            swap='right_forefinger4'),
        26:
        dict(
            name='left_forefinger3',
            id=26,
            color=[255, 153, 255],
            type='',
            swap='right_forefinger3'),
        27:
        dict(
            name='left_forefinger2',
            id=27,
            color=[255, 153, 255],
            type='',
            swap='right_forefinger2'),
        28:
        dict(
            name='left_forefinger1',
            id=28,
            color=[255, 153, 255],
            type='',
            swap='right_forefinger1'),
        29:
        dict(
            name='left_middle_finger4',
            id=29,
            color=[102, 178, 255],
            type='',
            swap='right_middle_finger4'),
        30:
        dict(
            name='left_middle_finger3',
            id=30,
            color=[102, 178, 255],
            type='',
            swap='right_middle_finger3'),
        31:
        dict(
            name='left_middle_finger2',
            id=31,
            color=[102, 178, 255],
            type='',
            swap='right_middle_finger2'),
        32:
        dict(
            name='left_middle_finger1',
            id=32,
            color=[102, 178, 255],
            type='',
            swap='right_middle_finger1'),
        33:
        dict(
            name='left_ring_finger4',
            id=33,
            color=[255, 51, 51],
            type='',
            swap='right_ring_finger4'),
        34:
        dict(
            name='left_ring_finger3',
            id=34,
            color=[255, 51, 51],
            type='',
            swap='right_ring_finger3'),
        35:
        dict(
            name='left_ring_finger2',
            id=35,
            color=[255, 51, 51],
            type='',
            swap='right_ring_finger2'),
        36:
        dict(
            name='left_ring_finger1',
            id=36,
            color=[255, 51, 51],
            type='',
            swap='right_ring_finger1'),
        37:
        dict(
            name='left_pinky_finger4',
            id=37,
            color=[0, 255, 0],
            type='',
            swap='right_pinky_finger4'),
        38:
        dict(
            name='left_pinky_finger3',
            id=38,
            color=[0, 255, 0],
            type='',
            swap='right_pinky_finger3'),
        39:
        dict(
            name='left_pinky_finger2',
            id=39,
            color=[0, 255, 0],
            type='',
            swap='right_pinky_finger2'),
        40:
        dict(
            name='left_pinky_finger1',
            id=40,
            color=[0, 255, 0],
            type='',
            swap='right_pinky_finger1'),
        41:
        dict(
            name='left_wrist',
            id=41,
            color=[255, 255, 255],
            type='',
            swap='right_wrist'),
    },
    skeleton_info={
        0:
        dict(link=('right_wrist', 'right_thumb1'), id=0, color=[255, 128, 0]),
        1:
        dict(link=('right_thumb1', 'right_thumb2'), id=1, color=[255, 128, 0]),
        2:
        dict(link=('right_thumb2', 'right_thumb3'), id=2, color=[255, 128, 0]),
        3:
        dict(link=('right_thumb3', 'right_thumb4'), id=3, color=[255, 128, 0]),
        4:
        dict(
            link=('right_wrist', 'right_forefinger1'),
            id=4,
            color=[255, 153, 255]),
        5:
        dict(
            link=('right_forefinger1', 'right_forefinger2'),
            id=5,
            color=[255, 153, 255]),
        6:
        dict(
            link=('right_forefinger2', 'right_forefinger3'),
            id=6,
            color=[255, 153, 255]),
        7:
        dict(
            link=('right_forefinger3', 'right_forefinger4'),
            id=7,
            color=[255, 153, 255]),
        8:
        dict(
            link=('right_wrist', 'right_middle_finger1'),
            id=8,
            color=[102, 178, 255]),
        9:
        dict(
            link=('right_middle_finger1', 'right_middle_finger2'),
            id=9,
            color=[102, 178, 255]),
        10:
        dict(
            link=('right_middle_finger2', 'right_middle_finger3'),
            id=10,
            color=[102, 178, 255]),
        11:
        dict(
            link=('right_middle_finger3', 'right_middle_finger4'),
            id=11,
            color=[102, 178, 255]),
        12:
        dict(
            link=('right_wrist', 'right_ring_finger1'),
            id=12,
            color=[255, 51, 51]),
        13:
        dict(
            link=('right_ring_finger1', 'right_ring_finger2'),
            id=13,
            color=[255, 51, 51]),
        14:
        dict(
            link=('right_ring_finger2', 'right_ring_finger3'),
            id=14,
            color=[255, 51, 51]),
        15:
        dict(
            link=('right_ring_finger3', 'right_ring_finger4'),
            id=15,
            color=[255, 51, 51]),
        16:
        dict(
            link=('right_wrist', 'right_pinky_finger1'),
            id=16,
            color=[0, 255, 0]),
        17:
        dict(
            link=('right_pinky_finger1', 'right_pinky_finger2'),
            id=17,
            color=[0, 255, 0]),
        18:
        dict(
            link=('right_pinky_finger2', 'right_pinky_finger3'),
            id=18,
            color=[0, 255, 0]),
        19:
        dict(
            link=('right_pinky_finger3', 'right_pinky_finger4'),
            id=19,
            color=[0, 255, 0]),
        20:
        dict(link=('left_wrist', 'left_thumb1'), id=20, color=[255, 128, 0]),
        21:
        dict(link=('left_thumb1', 'left_thumb2'), id=21, color=[255, 128, 0]),
        22:
        dict(link=('left_thumb2', 'left_thumb3'), id=22, color=[255, 128, 0]),
        23:
        dict(link=('left_thumb3', 'left_thumb4'), id=23, color=[255, 128, 0]),
        24:
        dict(
            link=('left_wrist', 'left_forefinger1'),
            id=24,
            color=[255, 153, 255]),
        25:
        dict(
            link=('left_forefinger1', 'left_forefinger2'),
            id=25,
            color=[255, 153, 255]),
        26:
        dict(
            link=('left_forefinger2', 'left_forefinger3'),
            id=26,
            color=[255, 153, 255]),
        27:
        dict(
            link=('left_forefinger3', 'left_forefinger4'),
            id=27,
            color=[255, 153, 255]),
        28:
        dict(
            link=('left_wrist', 'left_middle_finger1'),
            id=28,
            color=[102, 178, 255]),
        29:
        dict(
            link=('left_middle_finger1', 'left_middle_finger2'),
            id=29,
            color=[102, 178, 255]),
        30:
        dict(
            link=('left_middle_finger2', 'left_middle_finger3'),
            id=30,
            color=[102, 178, 255]),
        31:
        dict(
            link=('left_middle_finger3', 'left_middle_finger4'),
            id=31,
            color=[102, 178, 255]),
        32:
        dict(
            link=('left_wrist', 'left_ring_finger1'),
            id=32,
            color=[255, 51, 51]),
        33:
        dict(
            link=('left_ring_finger1', 'left_ring_finger2'),
            id=33,
            color=[255, 51, 51]),
        34:
        dict(
            link=('left_ring_finger2', 'left_ring_finger3'),
            id=34,
            color=[255, 51, 51]),
        35:
        dict(
            link=('left_ring_finger3', 'left_ring_finger4'),
            id=35,
            color=[255, 51, 51]),
        36:
        dict(
            link=('left_wrist', 'left_pinky_finger1'),
            id=36,
            color=[0, 255, 0]),
        37:
        dict(
            link=('left_pinky_finger1', 'left_pinky_finger2'),
            id=37,
            color=[0, 255, 0]),
        38:
        dict(
            link=('left_pinky_finger2', 'left_pinky_finger3'),
            id=38,
            color=[0, 255, 0]),
        39:
        dict(
            link=('left_pinky_finger3', 'left_pinky_finger4'),
            id=39,
            color=[0, 255, 0]),
    },
    joint_weights=[1.] * 42,
    sigmas=[])
