# Acu-RTMPOSE数据集配置 - COCO风格AP评估
# 支持分层评估锚点和穴位的AP指标

dataset_info = dict(
    dataset_name='acu_rtmpose',
    paper_info=dict(
        author='Acu-RTMPOSE Team',
        title='Acu-RTMPOSE: Hierarchical Acupoint Detection with Anchor-guided Feature Fusion',
        container='Custom Dataset',
        year='2024',
        homepage='https://github.com/acu-rtmpose',
    ),
    keypoint_info={
        # 穴位关键点 (0-22, 共23个)
        0: dict(name='hegu', id=0, color=[255, 0, 0], type='acupoint', swap=''),
        1: dict(name='laogong', id=1, color=[255, 0, 0], type='acupoint', swap=''),
        2: dict(name='taiyuan', id=2, color=[255, 0, 0], type='acupoint', swap=''),
        3: dict(name='shenmen', id=3, color=[255, 0, 0], type='acupoint', swap=''),
        4: dict(name='yuji', id=4, color=[255, 0, 0], type='acupoint', swap=''),
        5: dict(name='shaofu', id=5, color=[255, 0, 0], type='acupoint', swap=''),
        6: dict(name='daling', id=6, color=[255, 0, 0], type='acupoint', swap=''),
        7: dict(name='yangxi', id=7, color=[255, 0, 0], type='acupoint', swap=''),
        8: dict(name='yangchi', id=8, color=[255, 0, 0], type='acupoint', swap=''),
        9: dict(name='yanggu', id=9, color=[255, 0, 0], type='acupoint', swap=''),
        10: dict(name='wangu', id=10, color=[255, 0, 0], type='acupoint', swap=''),
        11: dict(name='shaoshang', id=11, color=[255, 0, 0], type='acupoint', swap=''),
        12: dict(name='shangyang', id=12, color=[255, 0, 0], type='acupoint', swap=''),
        13: dict(name='zhongchong', id=13, color=[255, 0, 0], type='acupoint', swap=''),
        14: dict(name='guanchong', id=14, color=[255, 0, 0], type='acupoint', swap=''),
        15: dict(name='shaochong', id=15, color=[255, 0, 0], type='acupoint', swap=''),
        16: dict(name='shixuan_thumb', id=16, color=[255, 0, 0], type='acupoint', swap=''),
        17: dict(name='shixuan_index', id=17, color=[255, 0, 0], type='acupoint', swap=''),
        18: dict(name='shixuan_middle', id=18, color=[255, 0, 0], type='acupoint', swap=''),
        19: dict(name='shixuan_ring', id=19, color=[255, 0, 0], type='acupoint', swap=''),
        20: dict(name='shixuan_pinky', id=20, color=[255, 0, 0], type='acupoint', swap=''),
        21: dict(name='baxie_1', id=21, color=[255, 0, 0], type='acupoint', swap=''),
        22: dict(name='baxie_2', id=22, color=[255, 0, 0], type='acupoint', swap=''),
        
        # 锚点关键点 (23-43, 共21个MediaPipe关键点)
        23: dict(name='wrist', id=23, color=[0, 0, 255], type='anchor', swap=''),
        24: dict(name='thumb_cmc', id=24, color=[0, 0, 255], type='anchor', swap=''),
        25: dict(name='thumb_mcp', id=25, color=[0, 0, 255], type='anchor', swap=''),
        26: dict(name='thumb_ip', id=26, color=[0, 0, 255], type='anchor', swap=''),
        27: dict(name='thumb_tip', id=27, color=[0, 0, 255], type='anchor', swap=''),
        28: dict(name='index_mcp', id=28, color=[0, 0, 255], type='anchor', swap=''),
        29: dict(name='index_pip', id=29, color=[0, 0, 255], type='anchor', swap=''),
        30: dict(name='index_dip', id=30, color=[0, 0, 255], type='anchor', swap=''),
        31: dict(name='index_tip', id=31, color=[0, 0, 255], type='anchor', swap=''),
        32: dict(name='middle_mcp', id=32, color=[0, 0, 255], type='anchor', swap=''),
        33: dict(name='middle_pip', id=33, color=[0, 0, 255], type='anchor', swap=''),
        34: dict(name='middle_dip', id=34, color=[0, 0, 255], type='anchor', swap=''),
        35: dict(name='middle_tip', id=35, color=[0, 0, 255], type='anchor', swap=''),
        36: dict(name='ring_mcp', id=36, color=[0, 0, 255], type='anchor', swap=''),
        37: dict(name='ring_pip', id=37, color=[0, 0, 255], type='anchor', swap=''),
        38: dict(name='ring_dip', id=38, color=[0, 0, 255], type='anchor', swap=''),
        39: dict(name='ring_tip', id=39, color=[0, 0, 255], type='anchor', swap=''),
        40: dict(name='pinky_mcp', id=40, color=[0, 0, 255], type='anchor', swap=''),
        41: dict(name='pinky_pip', id=41, color=[0, 0, 255], type='anchor', swap=''),
        42: dict(name='pinky_dip', id=42, color=[0, 0, 255], type='anchor', swap=''),
        43: dict(name='pinky_tip', id=43, color=[0, 0, 255], type='anchor', swap=''),
    },
    skeleton_info=[
        # 穴位连接（根据中医理论）
        dict(link=('hegu', 'laogong'), id=0, color=[255, 0, 0]),
        dict(link=('taiyuan', 'shenmen'), id=1, color=[255, 0, 0]),
        
        # 锚点连接（MediaPipe手部骨骼）
        dict(link=('wrist', 'thumb_cmc'), id=2, color=[0, 0, 255]),
        dict(link=('thumb_cmc', 'thumb_mcp'), id=3, color=[0, 0, 255]),
        dict(link=('thumb_mcp', 'thumb_ip'), id=4, color=[0, 0, 255]),
        dict(link=('thumb_ip', 'thumb_tip'), id=5, color=[0, 0, 255]),
        dict(link=('wrist', 'index_mcp'), id=6, color=[0, 0, 255]),
        dict(link=('index_mcp', 'index_pip'), id=7, color=[0, 0, 255]),
        dict(link=('index_pip', 'index_dip'), id=8, color=[0, 0, 255]),
        dict(link=('index_dip', 'index_tip'), id=9, color=[0, 0, 255]),
        dict(link=('wrist', 'middle_mcp'), id=10, color=[0, 0, 255]),
        dict(link=('middle_mcp', 'middle_pip'), id=11, color=[0, 0, 255]),
        dict(link=('middle_pip', 'middle_dip'), id=12, color=[0, 0, 255]),
        dict(link=('middle_dip', 'middle_tip'), id=13, color=[0, 0, 255]),
        dict(link=('wrist', 'ring_mcp'), id=14, color=[0, 0, 255]),
        dict(link=('ring_mcp', 'ring_pip'), id=15, color=[0, 0, 255]),
        dict(link=('ring_pip', 'ring_dip'), id=16, color=[0, 0, 255]),
        dict(link=('ring_dip', 'ring_tip'), id=17, color=[0, 0, 255]),
        dict(link=('wrist', 'pinky_mcp'), id=18, color=[0, 0, 255]),
        dict(link=('pinky_mcp', 'pinky_pip'), id=19, color=[0, 0, 255]),
        dict(link=('pinky_pip', 'pinky_dip'), id=20, color=[0, 0, 255]),
        dict(link=('pinky_dip', 'pinky_tip'), id=21, color=[0, 0, 255]),
    ],
    joint_weights=[1.0] * 44,  # 44个关键点的权重
    
    # 分层sigma配置
    sigmas=[
        # Acupoint sigmas (23 points) - smaller values for precise acupoints
        0.020, 0.025, 0.020, 0.020, 0.018,  # Important acupoints
        0.022, 0.020, 0.025, 0.022, 0.025,
        0.028, 0.028, 0.018, 0.022, 0.025,
        0.028, 0.022, 0.025, 0.022, 0.025,
        0.028, 0.025, 0.022,
        
        # Anchor sigmas (21 points) - based on MediaPipe hand model
        0.025,                               # WRIST
        0.035, 0.037, 0.047, 0.047,        # Thumb
        0.026, 0.025, 0.024, 0.035,        # Index finger
        0.026, 0.025, 0.024, 0.035,        # Middle finger
        0.026, 0.025, 0.024, 0.035,        # Ring finger
        0.026, 0.025, 0.024, 0.035,        # Pinky finger
    ]
)

# 数据集配置
dataset_name = 'acu_rtmpose'
data_mode = 'topdown'
data_root = '/root/autodl-tmp/datasets/mix/'

# 训练数据配置
train_dataloader = dict(
    batch_size=32,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=True),
    dataset=dict(
        type='CocoDataset',
        data_root=data_root,
        data_mode=data_mode,
        ann_file='train_with_mediapipe.json',
        data_prefix=dict(img='images/'),
        metainfo=dataset_info,
        pipeline=[],  # 在具体配置中定义
    ))

# 验证数据配置
val_dataloader = dict(
    batch_size=32,
    num_workers=4,
    persistent_workers=True,
    drop_last=False,
    sampler=dict(type='DefaultSampler', shuffle=False, round_up=False),
    dataset=dict(
        type='CocoDataset',
        data_root=data_root,
        data_mode=data_mode,
        ann_file='val_with_mediapipe.json',
        data_prefix=dict(img='images/'),
        metainfo=dataset_info,
        test_mode=True,
        pipeline=[],  # 在具体配置中定义
    ))

# 测试数据配置
test_dataloader = val_dataloader

# 评估器配置 - 使用新的AcuRTMPoseMetric
val_evaluator = dict(
    type='AcuRTMPoseMetric',
    ann_file=data_root + 'val_with_mediapipe.json',
    use_area=True,
    iou_type='keypoints',
    score_mode='bbox_keypoint',
    keypoint_score_thr=0.2,
    nms_mode='oks_nms',
    nms_thr=0.9,
    format_only=False,
    anchor_weight=0.4,      # 锚点权重
    acupoint_weight=0.6,    # 穴位权重（更重要）
    separate_eval=True,     # 分别评估锚点和穴位
)

test_evaluator = val_evaluator

# 可视化配置
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    type='PoseLocalVisualizer',
    vis_backends=vis_backends,
    name='visualizer')

# 默认钩子
default_hooks = dict(
    timer=dict(type='IterTimerHook'),
    logger=dict(type='LoggingHook', interval=50),
    param_scheduler=dict(type='ParamSchedulerHook'),
    checkpoint=dict(type='CheckpointHook', interval=10),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    visualization=dict(type='PoseVisualizationHook', enable=False),
)
