dataset_info = dict(
    dataset_name='acu_rtmpose',
    paper_info=dict(
        author='Acu-RTMPOSE Team',
        title='Hierarchical Hand Acupoint Detection with RTMPose',
        container='Custom Dataset for Traditional Chinese Medicine',
        year='2024',
        homepage='',
    ),
    keypoint_info={
        # Acupoint keypoints (0-22, total 23 points)
        0: dict(name='Shaoshang', id=0, color=[255, 0, 0], type='acupoint', swap=''),
        1: dict(name='Yuji', id=1, color=[255, 32, 0], type='acupoint', swap=''),
        2: dict(name='Taiyuan', id=2, color=[255, 64, 0], type='acupoint', swap=''),
        3: dict(name='Dal<PERSON>', id=3, color=[255, 96, 0], type='acupoint', swap=''),
        4: dict(name='Laogong', id=4, color=[255, 128, 0], type='acupoint', swap=''),
        5: dict(name='Zhongchong', id=5, color=[255, 160, 0], type='acupoint', swap=''),
        6: dict(name='Shenmen', id=6, color=[255, 192, 0], type='acupoint', swap=''),
        7: dict(name='Shaofu', id=7, color=[255, 224, 0], type='acupoint', swap=''),
        8: dict(name='Shaochong', id=8, color=[255, 255, 0], type='acupoint', swap=''),
        9: dict(name='Shangyang', id=9, color=[224, 255, 0], type='acupoint', swap=''),
        10: dict(name='Erjian', id=10, color=[192, 255, 0], type='acupoint', swap=''),
        11: dict(name='Sanjian', id=11, color=[160, 255, 0], type='acupoint', swap=''),
        12: dict(name='Hegu', id=12, color=[128, 255, 0], type='acupoint', swap=''),
        13: dict(name='Yangxi', id=13, color=[96, 255, 0], type='acupoint', swap=''),
        14: dict(name='Shaoze', id=14, color=[64, 255, 0], type='acupoint', swap=''),
        15: dict(name='Qiangu', id=15, color=[32, 255, 0], type='acupoint', swap=''),
        16: dict(name='Houxi', id=16, color=[0, 255, 0], type='acupoint', swap=''),
        17: dict(name='Wangu', id=17, color=[0, 255, 32], type='acupoint', swap=''),
        18: dict(name='Yanggu', id=18, color=[0, 255, 64], type='acupoint', swap=''),
        19: dict(name='Guanchong', id=19, color=[0, 255, 96], type='acupoint', swap=''),
        20: dict(name='Yemen', id=20, color=[0, 255, 128], type='acupoint', swap=''),
        21: dict(name='Zhongzhu', id=21, color=[0, 255, 160], type='acupoint', swap=''),
        22: dict(name='Yangchi', id=22, color=[0, 255, 192], type='acupoint', swap=''),
        
        # MediaPipe anchor keypoints (23-43, total 21 points)
        23: dict(name='WRIST', id=23, color=[0, 255, 255], type='anchor', swap=''),
        24: dict(name='THUMB_CMC', id=24, color=[0, 224, 255], type='anchor', swap=''),
        25: dict(name='THUMB_MCP', id=25, color=[0, 192, 255], type='anchor', swap=''),
        26: dict(name='THUMB_IP', id=26, color=[0, 160, 255], type='anchor', swap=''),
        27: dict(name='THUMB_TIP', id=27, color=[0, 128, 255], type='anchor', swap=''),
        28: dict(name='INDEX_FINGER_MCP', id=28, color=[0, 96, 255], type='anchor', swap=''),
        29: dict(name='INDEX_FINGER_PIP', id=29, color=[0, 64, 255], type='anchor', swap=''),
        30: dict(name='INDEX_FINGER_DIP', id=30, color=[0, 32, 255], type='anchor', swap=''),
        31: dict(name='INDEX_FINGER_TIP', id=31, color=[0, 0, 255], type='anchor', swap=''),
        32: dict(name='MIDDLE_FINGER_MCP', id=32, color=[32, 0, 255], type='anchor', swap=''),
        33: dict(name='MIDDLE_FINGER_PIP', id=33, color=[64, 0, 255], type='anchor', swap=''),
        34: dict(name='MIDDLE_FINGER_DIP', id=34, color=[96, 0, 255], type='anchor', swap=''),
        35: dict(name='MIDDLE_FINGER_TIP', id=35, color=[128, 0, 255], type='anchor', swap=''),
        36: dict(name='RING_FINGER_MCP', id=36, color=[160, 0, 255], type='anchor', swap=''),
        37: dict(name='RING_FINGER_PIP', id=37, color=[192, 0, 255], type='anchor', swap=''),
        38: dict(name='RING_FINGER_DIP', id=38, color=[224, 0, 255], type='anchor', swap=''),
        39: dict(name='RING_FINGER_TIP', id=39, color=[255, 0, 255], type='anchor', swap=''),
        40: dict(name='PINKY_MCP', id=40, color=[255, 0, 224], type='anchor', swap=''),
        41: dict(name='PINKY_PIP', id=41, color=[255, 0, 192], type='anchor', swap=''),
        42: dict(name='PINKY_DIP', id=42, color=[255, 0, 160], type='anchor', swap=''),
        43: dict(name='PINKY_TIP', id=43, color=[255, 0, 128], type='anchor', swap=''),
    },
    skeleton_info={
        # # Acupoint connections (based on meridian pathways)
        # # Lung meridian (手太阴肺经)
        # 0: dict(link=('Shaoshang', 'Yuji'), id=0, color=[255, 64, 64]),
        # 1: dict(link=('Yuji', 'Taiyuan'), id=1, color=[255, 64, 64]),
        
        # # Heart meridian (手少阴心经)  
        # 2: dict(link=('Shaochong', 'Shaofu'), id=2, color=[255, 128, 128]),
        # 3: dict(link=('Shaofu', 'Shenmen'), id=3, color=[255, 128, 128]),
        
        # # Pericardium meridian (手厥阴心包经)
        # 4: dict(link=('Zhongchong', 'Laogong'), id=4, color=[255, 192, 128]),
        # 5: dict(link=('Laogong', 'Daling'), id=5, color=[255, 192, 128]),
        
        # # Large intestine meridian (手阳明大肠经)
        # 6: dict(link=('Shangyang', 'Erjian'), id=6, color=[128, 255, 128]),
        # 7: dict(link=('Erjian', 'Sanjian'), id=7, color=[128, 255, 128]),
        # 8: dict(link=('Sanjian', 'Hegu'), id=8, color=[128, 255, 128]),
        # 9: dict(link=('Hegu', 'Yangxi'), id=9, color=[128, 255, 128]),
        
        # # Small intestine meridian (手太阳小肠经)
        # 10: dict(link=('Shaoze', 'Qiangu'), id=10, color=[128, 128, 255]),
        # 11: dict(link=('Qiangu', 'Houxi'), id=11, color=[128, 128, 255]),
        # 12: dict(link=('Houxi', 'Wangu'), id=12, color=[128, 128, 255]),
        # 13: dict(link=('Wangu', 'Yanggu'), id=13, color=[128, 128, 255]),
        
        # # Triple energizer meridian (手少阳三焦经)
        # 14: dict(link=('Guanchong', 'Yemen'), id=14, color=[255, 128, 255]),
        # 15: dict(link=('Yemen', 'Zhongzhu'), id=15, color=[255, 128, 255]),
        # 16: dict(link=('Zhongzhu', 'Yangchi'), id=16, color=[255, 128, 255]),
        
        # # MediaPipe anchor connections
        # # Thumb
        # 17: dict(link=('WRIST', 'THUMB_CMC'), id=17, color=[128, 255, 255]),
        # 18: dict(link=('THUMB_CMC', 'THUMB_MCP'), id=18, color=[128, 255, 255]),
        # 19: dict(link=('THUMB_MCP', 'THUMB_IP'), id=19, color=[128, 255, 255]),
        # 20: dict(link=('THUMB_IP', 'THUMB_TIP'), id=20, color=[128, 255, 255]),
        
        # # Index finger
        # 21: dict(link=('WRIST', 'INDEX_FINGER_MCP'), id=21, color=[255, 255, 128]),
        # 22: dict(link=('INDEX_FINGER_MCP', 'INDEX_FINGER_PIP'), id=22, color=[255, 255, 128]),
        # 23: dict(link=('INDEX_FINGER_PIP', 'INDEX_FINGER_DIP'), id=23, color=[255, 255, 128]),
        # 24: dict(link=('INDEX_FINGER_DIP', 'INDEX_FINGER_TIP'), id=24, color=[255, 255, 128]),
        
        # # Middle finger
        # 25: dict(link=('WRIST', 'MIDDLE_FINGER_MCP'), id=25, color=[128, 128, 255]),
        # 26: dict(link=('MIDDLE_FINGER_MCP', 'MIDDLE_FINGER_PIP'), id=26, color=[128, 128, 255]),
        # 27: dict(link=('MIDDLE_FINGER_PIP', 'MIDDLE_FINGER_DIP'), id=27, color=[128, 128, 255]),
        # 28: dict(link=('MIDDLE_FINGER_DIP', 'MIDDLE_FINGER_TIP'), id=28, color=[128, 128, 255]),
        
        # # Ring finger
        # 29: dict(link=('WRIST', 'RING_FINGER_MCP'), id=29, color=[255, 128, 128]),
        # 30: dict(link=('RING_FINGER_MCP', 'RING_FINGER_PIP'), id=30, color=[255, 128, 128]),
        # 31: dict(link=('RING_FINGER_PIP', 'RING_FINGER_DIP'), id=31, color=[255, 128, 128]),
        # 32: dict(link=('RING_FINGER_DIP', 'RING_FINGER_TIP'), id=32, color=[255, 128, 128]),
        
        # # Pinky finger
        # 33: dict(link=('WRIST', 'PINKY_MCP'), id=33, color=[128, 255, 128]),
        # 34: dict(link=('PINKY_MCP', 'PINKY_PIP'), id=34, color=[128, 255, 128]),
        # 35: dict(link=('PINKY_PIP', 'PINKY_DIP'), id=35, color=[128, 255, 128]),
        # 36: dict(link=('PINKY_DIP', 'PINKY_TIP'), id=36, color=[128, 255, 128]),
    },
    joint_weights=[
        # Acupoint weights (23 points) - higher weights for important acupoints
        1.5, 1.2, 1.5, 1.5, 1.8,  # Shaoshang, Yuji, Taiyuan, Daling, Laogong
        1.3, 1.5, 1.2, 1.3, 1.2,  # Zhongchong, Shenmen, Shaofu, Shaochong, Shangyang
        1.1, 1.1, 1.8, 1.3, 1.2,  # Erjian, Sanjian, Hegu, Yangxi, Shaoze
        1.1, 1.3, 1.2, 1.3, 1.2,  # Qiangu, Houxi, Wangu, Yanggu, Guanchong
        1.1, 1.2, 1.3,             # Yemen, Zhongzhu, Yangchi
        
        # Anchor weights (21 points) - structural importance
        1.8,                        # WRIST (most important)
        1.2, 1.1, 1.0, 1.0,        # Thumb
        1.3, 1.1, 1.0, 1.0,        # Index finger
        1.3, 1.1, 1.0, 1.0,        # Middle finger  
        1.2, 1.0, 1.0, 1.0,        # Ring finger
        1.2, 1.0, 1.0, 1.0,        # Pinky finger
    ],
    sigmas=[
        # Acupoint sigmas (23 points) - smaller values for precise acupoints
        0.020, 0.025, 0.020, 0.020, 0.018,  # Important acupoints
        0.022, 0.020, 0.025, 0.022, 0.025,
        0.028, 0.028, 0.018, 0.022, 0.025,
        0.028, 0.022, 0.025, 0.022, 0.025,
        0.028, 0.025, 0.022,
        
        # Anchor sigmas (21 points) - based on MediaPipe hand model
        0.025,                               # WRIST
        0.035, 0.037, 0.047, 0.047,        # Thumb
        0.026, 0.025, 0.024, 0.035,        # Index finger
        0.026, 0.025, 0.024, 0.035,        # Middle finger
        0.026, 0.025, 0.024, 0.035,        # Ring finger
        0.026, 0.025, 0.024, 0.035,        # Pinky finger
    ]
)
