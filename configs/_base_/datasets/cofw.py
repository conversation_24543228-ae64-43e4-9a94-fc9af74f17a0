dataset_info = dict(
    dataset_name='cofw',
    paper_info=dict(
        author='Burgos-<PERSON>izzu, Xavier <PERSON> and <PERSON><PERSON>, '
        r'<PERSON> and <PERSON>{\'a}r, Piotr',
        title='Robust face landmark estimation under occlusion',
        container='Proceedings of the IEEE international '
        'conference on computer vision',
        year='2013',
        homepage='http://www.vision.caltech.edu/xpburgos/ICCV13/',
    ),
    keypoint_info={
        0: dict(name='kpt-0', id=0, color=[255, 0, 0], type='', swap='kpt-1'),
        1: dict(name='kpt-1', id=1, color=[255, 0, 0], type='', swap='kpt-0'),
        2: dict(name='kpt-2', id=2, color=[255, 0, 0], type='', swap='kpt-3'),
        3: dict(name='kpt-3', id=3, color=[255, 0, 0], type='', swap='kpt-2'),
        4: dict(name='kpt-4', id=4, color=[255, 0, 0], type='', swap='kpt-6'),
        5: dict(name='kpt-5', id=5, color=[255, 0, 0], type='', swap='kpt-7'),
        6: dict(name='kpt-6', id=6, color=[255, 0, 0], type='', swap='kpt-4'),
        7: dict(name='kpt-7', id=7, color=[255, 0, 0], type='', swap='kpt-5'),
        8: dict(name='kpt-8', id=8, color=[255, 0, 0], type='', swap='kpt-9'),
        9: dict(name='kpt-9', id=9, color=[255, 0, 0], type='', swap='kpt-8'),
        10:
        dict(name='kpt-10', id=10, color=[255, 0, 0], type='', swap='kpt-11'),
        11:
        dict(name='kpt-11', id=11, color=[255, 0, 0], type='', swap='kpt-10'),
        12:
        dict(name='kpt-12', id=12, color=[255, 0, 0], type='', swap='kpt-14'),
        13:
        dict(name='kpt-13', id=13, color=[255, 0, 0], type='', swap='kpt-15'),
        14:
        dict(name='kpt-14', id=14, color=[255, 0, 0], type='', swap='kpt-12'),
        15:
        dict(name='kpt-15', id=15, color=[255, 0, 0], type='', swap='kpt-13'),
        16:
        dict(name='kpt-16', id=16, color=[255, 0, 0], type='', swap='kpt-17'),
        17:
        dict(name='kpt-17', id=17, color=[255, 0, 0], type='', swap='kpt-16'),
        18:
        dict(name='kpt-18', id=18, color=[255, 0, 0], type='', swap='kpt-19'),
        19:
        dict(name='kpt-19', id=19, color=[255, 0, 0], type='', swap='kpt-18'),
        20: dict(name='kpt-20', id=20, color=[255, 0, 0], type='', swap=''),
        21: dict(name='kpt-21', id=21, color=[255, 0, 0], type='', swap=''),
        22:
        dict(name='kpt-22', id=22, color=[255, 0, 0], type='', swap='kpt-23'),
        23:
        dict(name='kpt-23', id=23, color=[255, 0, 0], type='', swap='kpt-22'),
        24: dict(name='kpt-24', id=24, color=[255, 0, 0], type='', swap=''),
        25: dict(name='kpt-25', id=25, color=[255, 0, 0], type='', swap=''),
        26: dict(name='kpt-26', id=26, color=[255, 0, 0], type='', swap=''),
        27: dict(name='kpt-27', id=27, color=[255, 0, 0], type='', swap=''),
        28: dict(name='kpt-28', id=28, color=[255, 0, 0], type='', swap='')
    },
    skeleton_info={},
    joint_weights=[1.] * 29,
    sigmas=[])
