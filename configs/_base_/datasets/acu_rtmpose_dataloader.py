# Dataset configuration for Acu-RTMPOSE
# This configuration supports dual-task learning with acupoints and MediaPipe anchors

# Dataset settings
dataset_type = 'AcuRTMPoseDataset'
data_mode = 'topdown'
data_root = '/root/autodl-tmp/datasets/mix/'

# Data pipeline for training - 优化的Acu-RTMPOSE训练pipeline
train_pipeline = [
    # 1. 基础数据加载
    dict(type='LoadImage'),
    dict(type='GetBBoxCenterScale', padding=1.25),

    # 2. Acu-RTMPOSE专用关键点分离
    dict(type='AcuRTMPoseKeypointSeparator'),

    # 3. 数据增强 - 考虑穴位特殊性
    dict(type='AcuRTMPoseRandomFlip', prob=0.5),  # 使用专用翻转
    dict(type='RandomHalfBody', prob=0.2),  # 降低半身概率，保持手部完整性
    dict(
        type='RandomBBoxTransform',
        scale_factor=[0.7, 1.3],  # 减小缩放范围，保持穴位精度
        rotate_factor=60,         # 减小旋转角度，避免穴位位置失真
        shift_factor=0.15),       # 减小平移，保持手部在中心

    # 4. 仿射变换
    dict(type='TopdownAffine', input_size=(256, 192)),

    # 5. 颜色增强 - 适度增强，保持穴位可见性
    dict(type='mmdet.YOLOXHSVRandomAug'),

    # 6. 高级数据增强 - 针对医学图像优化
    dict(
        type='Albumentation',
        transforms=[
            # 轻微模糊，模拟真实拍摄条件
            dict(type='Blur', blur_limit=3, p=0.1),
            dict(type='MedianBlur', blur_limit=3, p=0.1),

            # 减少遮挡，保护重要穴位区域
            dict(
                type='CoarseDropout',
                max_holes=1,
                max_height=0.3,  # 减小遮挡区域
                max_width=0.3,
                min_holes=1,
                min_height=0.1,
                min_width=0.1,
                p=0.3),  # 降低遮挡概率

            # 添加噪声，提高鲁棒性
            dict(type='GaussNoise', var_limit=(10.0, 50.0), p=0.2),

            # 亮度对比度调整，适应不同光照条件
            dict(type='RandomBrightnessContrast',
                 brightness_limit=0.2, contrast_limit=0.2, p=0.3),
        ]),

    # 7. Acu-RTMPOSE专用目标生成
    dict(
        type='AcuRTMPoseGenerateTarget',
        input_size=(256, 192),
        simcc_split_ratio=2.0,
        sigma=(4.9, 5.66),
        normalize=False,
        use_dark=False),

    # 8. 数据打包
    dict(type='AcuRTMPosePackInputs')
]

# Data pipeline for validation/testing - 简化的推理pipeline
val_pipeline = [
    # 1. 基础数据加载
    dict(type='LoadImage'),
    dict(type='GetBBoxCenterScale', padding=1.25),

    # 2. Acu-RTMPOSE专用关键点分离
    dict(type='AcuRTMPoseKeypointSeparator'),

    # 3. 仿射变换（无数据增强）
    dict(type='TopdownAffine', input_size=(256, 192)),

    # 4. Acu-RTMPOSE专用目标生成
    dict(
        type='AcuRTMPoseGenerateTarget',
        input_size=(256, 192),
        simcc_split_ratio=2.0,
        sigma=(4.9, 5.66),
        normalize=False,
        use_dark=False),

    # 5. 数据打包
    dict(type='AcuRTMPosePackInputs')
]

# 推理专用pipeline（无标签生成）
test_pipeline = [
    # 1. 基础数据加载
    dict(type='LoadImage'),
    dict(type='GetBBoxCenterScale', padding=1.25),

    # 2. 仿射变换
    dict(type='TopdownAffine', input_size=(256, 192)),

    # 3. 简单打包（推理时不需要标签）
    dict(type='PackPoseInputs')
]

# Training dataset
train_dataloader = dict(
    batch_size=32,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=True),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        data_mode=data_mode,
        ann_file='train_with_mediapipe.json',
        data_prefix=dict(img='images/'),
        pipeline=train_pipeline,
        metainfo=dict(from_file='configs/_base_/datasets/acu_rtmpose.py'),
    ))

# Validation dataset
val_dataloader = dict(
    batch_size=32,
    num_workers=4,
    persistent_workers=True,
    drop_last=False,
    sampler=dict(type='DefaultSampler', shuffle=False, round_up=False),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        data_mode=data_mode,
        ann_file='val_with_mediapipe.json',
        data_prefix=dict(img='images/'),
        pipeline=val_pipeline,
        test_mode=True,
        metainfo=dict(from_file='configs/_base_/datasets/acu_rtmpose.py'),
    ))

# Test dataset (same as validation for now)
test_dataloader = val_dataloader

# Evaluation metrics
val_evaluator = dict(
    type='CocoMetric',
    ann_file=data_root + 'val_with_mediapipe.json',
    use_area=True,
    iou_type='keypoints',
    prefix='acu_rtmpose')

test_evaluator = val_evaluator

# Additional dataset-specific configurations
dataset_info = dict(
    dataset_name='acu_rtmpose',
    num_keypoints=44,  # 23 acupoints + 21 anchors
    num_acupoints=23,
    num_anchors=21,
    acupoint_indices=list(range(23)),      # 0-22
    anchor_indices=list(range(23, 44)),    # 23-43
    input_size=(256, 192),
    simcc_split_ratio=2.0,
)

# Data loading settings
data = dict(
    samples_per_gpu=32,
    workers_per_gpu=4,
    train=dict(
        type=dataset_type,
        data_root=data_root,
        data_mode=data_mode,
        ann_file='train_with_mediapipe.json',
        data_prefix=dict(img='images/'),
        pipeline=train_pipeline,
        metainfo=dict(from_file='configs/_base_/datasets/acu_rtmpose.py'),
    ),
    val=dict(
        type=dataset_type,
        data_root=data_root,
        data_mode=data_mode,
        ann_file='val_with_mediapipe.json',
        data_prefix=dict(img='images/'),
        pipeline=val_pipeline,
        test_mode=True,
        metainfo=dict(from_file='configs/_base_/datasets/acu_rtmpose.py'),
    ),
    test=dict(
        type=dataset_type,
        data_root=data_root,
        data_mode=data_mode,
        ann_file='val_with_mediapipe.json',
        data_prefix=dict(img='images/'),
        pipeline=val_pipeline,
        test_mode=True,
        metainfo=dict(from_file='configs/_base_/datasets/acu_rtmpose.py'),
    ),
)
