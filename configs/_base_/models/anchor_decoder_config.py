# Acu-RTMPOSE锚点解码器配置
# 第一阶段：MediaPipe手部骨骼锚点检测

# 模型配置
model = dict(
    type='TopdownPoseEstimator',
    data_preprocessor=dict(
        type='PoseDataPreprocessor',
        mean=[123.675, 116.28, 103.53],
        std=[58.395, 57.12, 57.375],
        bgr_to_rgb=True),
    backbone=dict(
        type='CSPNeXt',
        arch='P5',
        expand_ratio=0.5,
        deepen_factor=0.67,  # m版本
        widen_factor=0.75,   # m版本
        out_indices=(4, ),
        channel_attention=True,
        norm_cfg=dict(type='SyncBN'),
        act_cfg=dict(type='SiLU'),
        init_cfg=dict(
            type='Pretrained',
            prefix='backbone.',
            checkpoint='https://download.openmmlab.com/mmpose/v1/projects/'
                      'rtmposev1/cspnext-m_udp-aic-coco_210e-256x192-f2f7d6f6_20230130.pth'  # noqa
        )),
    head=dict(
        type='AnchorDecoder',
        in_channels=768,  # CSPNeXt-m输出通道
        out_channels=21,  # 21个MediaPipe锚点
        input_size=(256, 192),
        in_featuremap_size=(8, 6),  # 256/32=8, 192/32=6
        simcc_split_ratio=2.0,
        final_layer_kernel_size=7,  # 大核卷积增大感受野
        gau_cfg=dict(
            hidden_dims=256,
            s=128,
            expansion_factor=2,
            dropout_rate=0.0,
            drop_path=0.0,
            act_fn='SiLU',
            use_rel_bias=False,
            pos_enc=False),
        loss=dict(
            type='KLDiscretLoss', 
            use_target_weight=True,
            beta=10.0),  # 适当的beta值
        decoder=dict(
            type='SimCCLabel',
            input_size=(256, 192),
            sigma=(4.9, 5.66),
            simcc_split_ratio=2.0,
            normalize=False,
            use_dark=False)),
    test_cfg=dict(
        flip_test=True,
        flip_mode='heatmap',
        shift_heatmap=False,
    ))

# 训练配置
train_cfg = dict(max_epochs=420, val_interval=10)

# 优化器配置 - 针对锚点检测优化
optim_wrapper = dict(
    optimizer=dict(
        type='AdamW',
        lr=4e-3,  # 基础学习率
        betas=(0.9, 0.999),
        weight_decay=0.05),
    paramwise_cfg=dict(
        norm_decay_mult=0,
        bias_decay_mult=0,
        bypass_duplicate=True))

# 学习率调度
param_scheduler = [
    dict(
        type='LinearLR',
        start_factor=5e-4,
        by_epoch=False,
        begin=0,
        end=1000),
    dict(
        type='CosineAnnealingLR',
        eta_min=4e-5,
        begin=1000,
        end=420000,
        T_max=419000,
        by_epoch=False,
        convert_to_iter_based=True),
]

# 自动缩放学习率
auto_scale_lr = dict(base_batch_size=1024)

# 默认钩子
default_hooks = dict(
    checkpoint=dict(
        save_best='acu_rtmpose/acc_pose_anchor',  # 保存锚点准确率最高的模型
        rule='greater',
        max_keep_ckpts=1),
    logger=dict(interval=50),
    param_scheduler=dict(type='ParamSchedulerHook'),
    timer=dict(type='IterTimerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
)

# 可视化配置
vis_backends = [
    dict(type='LocalVisBackend'),
    dict(type='TensorboardVisBackend'),
]
visualizer = dict(
    type='PoseLocalVisualizer',
    vis_backends=vis_backends,
    name='visualizer')

# 日志配置
log_processor = dict(
    type='LogProcessor',
    window_size=50,
    by_epoch=True,
    num_digits=6)
log_level = 'INFO'

# 加载和恢复
load_from = None
resume = False

# 锚点特定的评估配置
anchor_evaluation = dict(
    # MediaPipe手部关键点的特殊评估指标
    keypoint_info=dict(
        # 重要锚点（权重更高）
        important_anchors=[0, 4, 8, 12, 16, 20],  # 手腕和指尖
        # 结构性锚点（用于结构评估）
        structural_anchors=[0, 1, 5, 9, 13, 17],  # 手腕和各指根部
    ),
    # 评估阈值
    pck_thresholds=[0.05, 0.1, 0.2, 0.3, 0.4, 0.5],
    # 归一化方式
    normalize_factor='bbox_size',  # 使用bbox大小归一化
)

# 锚点检测的特殊配置
anchor_specific_cfg = dict(
    # 数据增强策略（相对保守，保护骨骼结构）
    augmentation_strength='medium',
    
    # 损失权重
    loss_weights=dict(
        loss_anchor_x=1.0,
        loss_anchor_y=1.0,
    ),
    
    # 后处理配置
    post_processing=dict(
        # 是否使用DARK后处理
        use_dark=False,
        # 是否使用高斯滤波
        use_gaussian_blur=True,
        gaussian_kernel_size=3,
        # 置信度阈值
        confidence_threshold=0.3,
    ),
    
    # 推理优化
    inference_optimization=dict(
        # 是否使用TensorRT
        use_tensorrt=False,
        # 是否使用半精度
        use_fp16=True,
        # 批处理大小
        batch_size=8,
    ),
)

# 模型复杂度分析
model_complexity = dict(
    # 预期的模型大小和计算量
    expected_params='~1.5M',  # 锚点解码器参数量
    expected_flops='~0.5G',   # 计算量
    expected_latency='~5ms',  # 推理延迟（GPU）
    
    # 性能目标
    target_accuracy=dict(
        pck_0_1=0.95,  # PCK@0.1阈值下的准确率
        pck_0_2=0.98,  # PCK@0.2阈值下的准确率
    ),
)

# 调试和开发配置
debug_cfg = dict(
    # 是否启用调试模式
    debug_mode=False,
    
    # 可视化配置
    visualization=dict(
        # 是否保存预测结果
        save_predictions=True,
        # 是否绘制注意力图
        draw_attention_maps=False,
        # 是否显示特征图
        show_feature_maps=False,
    ),
    
    # 日志详细程度
    log_level='INFO',
    
    # 是否启用梯度检查
    gradient_check=False,
)
