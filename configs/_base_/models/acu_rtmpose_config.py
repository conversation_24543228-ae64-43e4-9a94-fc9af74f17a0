# Acu-RTMPOSE完整模型配置
# 层次化穴位检测：锚点解码器 + 信息融合模块 + 穴位解码器

# 模型配置
model = dict(
    type='AcuRTMPose',
    data_preprocessor=dict(
        type='PoseDataPreprocessor',
        mean=[123.675, 116.28, 103.53],
        std=[58.395, 57.12, 57.375],
        bgr_to_rgb=True),
    
    # Backbone: CSPNeXt-m
    backbone=dict(
        type='CSPNeXt',
        arch='P5',
        expand_ratio=0.5,
        deepen_factor=0.67,  # m版本
        widen_factor=0.75,   # m版本
        out_indices=(4, ),
        channel_attention=True,
        norm_cfg=dict(type='SyncBN'),
        act_cfg=dict(type='SiLU'),
        init_cfg=dict(
            type='Pretrained',
            prefix='backbone.',
            checkpoint='https://download.openmmlab.com/mmpose/v1/projects/'
                      'rtmposev1/cspnext-m_udp-aic-coco_210e-256x192-f2f7d6f6_20230130.pth'
        )),
    
    # 第一阶段：锚点解码器
    anchor_head=dict(
        type='AnchorDecoder',
        in_channels=768,  # CSPNeXt-m输出通道
        out_channels=21,  # 21个MediaPipe锚点
        input_size=(256, 192),
        in_featuremap_size=(8, 6),  # 256/32=8, 192/32=6
        simcc_split_ratio=2.0,
        final_layer_kernel_size=7,  # 大核卷积
        gau_cfg=dict(
            hidden_dims=256,
            s=128,
            expansion_factor=2,
            dropout_rate=0.0,
            drop_path=0.0,
            act_fn='SiLU',
            use_rel_bias=False,
            pos_enc=False),
        loss=dict(
            type='KLDiscretLoss', 
            use_target_weight=True,
            beta=10.0),
        decoder=dict(
            type='SimCCLabel',
            input_size=(256, 192),
            sigma=(4.9, 5.66),
            simcc_split_ratio=2.0,
            normalize=False,
            use_dark=False)),
    
    # 信息融合模块
    fusion_module=dict(
        type='AnchorFeatureFusion',
        backbone_channels=768,      # 与backbone输出通道一致
        anchor_channels=256,        # 锚点特征维度
        fusion_channels=768,        # 融合后特征维度
        num_anchors=21,            # MediaPipe锚点数量
        fusion_type='attention',    # 融合策略: 'attention'|'concat'|'add'
        use_position_encoding=True, # 使用位置编码
        dropout_rate=0.1),
    
    # 第二阶段：穴位解码器（可选）
    acupoint_head=dict(
        type='AcupointDecoder',  # 将在Task 2.3中实现
        in_channels=768,         # 融合特征通道
        out_channels=23,         # 23个穴位
        input_size=(256, 192),
        in_featuremap_size=(8, 6),
        simcc_split_ratio=2.0,
        final_layer_kernel_size=5,  # 相对较小的核
        gau_cfg=dict(
            hidden_dims=256,
            s=128,
            expansion_factor=2,
            dropout_rate=0.0,
            drop_path=0.0,
            act_fn='SiLU',
            use_rel_bias=False,
            pos_enc=False),
        loss=dict(
            type='KLDiscretLoss', 
            use_target_weight=True,
            beta=10.0),
        decoder=dict(
            type='SimCCLabel',
            input_size=(256, 192),
            sigma=(3.0, 3.5),  # 更小的sigma，要求更高精度
            simcc_split_ratio=2.0,
            normalize=False,
            use_dark=False)),
    
    # 测试配置
    test_cfg=dict(
        flip_test=True,
        flip_mode='heatmap',
        shift_heatmap=False,
    ))

# 训练配置
train_cfg = dict(max_epochs=420, val_interval=10)  # 每10个epoch验证一轮

# 优化器配置 - 分层学习率
optim_wrapper = dict(
    optimizer=dict(
        type='AdamW',
        lr=4e-3,  # 基础学习率
        betas=(0.9, 0.999),
        weight_decay=0.05),
    paramwise_cfg=dict(
        norm_decay_mult=0,
        bias_decay_mult=0,
        bypass_duplicate=True,
        # 分层学习率设置
        custom_keys={
            'backbone': dict(lr_mult=0.1),      # backbone较低学习率
            'anchor_head': dict(lr_mult=1.0),   # 锚点解码器标准学习率
            'fusion_module': dict(lr_mult=1.0), # 融合模块标准学习率
            'acupoint_head': dict(lr_mult=1.5), # 穴位解码器较高学习率
        }))

# 学习率调度
param_scheduler = [
    dict(
        type='LinearLR',
        start_factor=5e-4,
        by_epoch=False,
        begin=0,
        end=1000),
    dict(
        type='CosineAnnealingLR',
        eta_min=4e-5,
        begin=1000,
        end=420000,
        T_max=419000,
        by_epoch=False,
        convert_to_iter_based=True),
]

# 自动缩放学习率
auto_scale_lr = dict(base_batch_size=1024)

# 默认钩子
default_hooks = dict(
    checkpoint=dict(
        save_best='acu_rtmpose/acc_pose_combined',  # 保存综合准确率最高的模型
        rule='greater',
        max_keep_ckpts=1),
    logger=dict(interval=50),
    param_scheduler=dict(type='ParamSchedulerHook'),
    timer=dict(type='IterTimerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
)

# 可视化配置
vis_backends = [
    dict(type='LocalVisBackend'),
    dict(type='TensorboardVisBackend'),
]
visualizer = dict(
    type='PoseLocalVisualizer',
    vis_backends=vis_backends,
    name='visualizer')

# 日志配置
log_processor = dict(
    type='LogProcessor',
    window_size=50,
    by_epoch=True,
    num_digits=6)
log_level = 'INFO'

# 加载和恢复
load_from = None
resume = False

# 层次化训练策略
hierarchical_training = dict(
    # 阶段1：只训练锚点解码器
    stage1=dict(
        epochs=100,
        freeze_modules=['acupoint_head'],  # 冻结穴位解码器
        loss_weights=dict(
            anchor_loss=1.0,
            acupoint_loss=0.0,  # 不计算穴位损失
        )
    ),
    
    # 阶段2：联合训练，较低的穴位损失权重
    stage2=dict(
        epochs=200,
        freeze_modules=[],  # 解冻所有模块
        loss_weights=dict(
            anchor_loss=1.0,
            acupoint_loss=0.5,  # 较低的穴位损失权重
        )
    ),
    
    # 阶段3：全面训练，平衡的损失权重
    stage3=dict(
        epochs=120,
        freeze_modules=[],
        loss_weights=dict(
            anchor_loss=1.0,
            acupoint_loss=1.0,  # 平衡的损失权重
        )
    ),
)

# 评估配置
evaluation = dict(
    # 分别评估锚点和穴位
    anchor_metrics=dict(
        pck_thresholds=[0.05, 0.1, 0.2, 0.3, 0.4, 0.5],
        normalize_factor='bbox_size',
        important_keypoints=[0, 4, 8, 12, 16, 20],  # 手腕和指尖
    ),
    
    acupoint_metrics=dict(
        pck_thresholds=[0.02, 0.05, 0.1, 0.15, 0.2],  # 更严格的阈值
        normalize_factor='bbox_size',
        important_acupoints=[0, 4, 12, 18, 22],  # 重要穴位
    ),
    
    # 综合评估
    combined_metrics=dict(
        anchor_weight=0.4,   # 锚点权重
        acupoint_weight=0.6, # 穴位权重（更重要）
    ),
)

# 数据增强策略（针对层次化训练优化）
data_augmentation = dict(
    # 阶段1：较强的增强，专注于锚点鲁棒性
    stage1=dict(
        rotation_factor=80,
        scale_factor=[0.6, 1.4],
        flip_prob=0.6,
        noise_factor=0.3,
    ),
    
    # 阶段2：中等增强，平衡锚点和穴位
    stage2=dict(
        rotation_factor=60,
        scale_factor=[0.7, 1.3],
        flip_prob=0.5,
        noise_factor=0.2,
    ),
    
    # 阶段3：较轻增强，专注于穴位精度
    stage3=dict(
        rotation_factor=30,
        scale_factor=[0.8, 1.2],
        flip_prob=0.4,
        noise_factor=0.1,
    ),
)

# 模型复杂度分析
model_complexity = dict(
    # 各模块的预期参数量和计算量
    backbone=dict(params='~25M', flops='~8G'),
    anchor_head=dict(params='~1.5M', flops='~0.5G'),
    fusion_module=dict(params='~2M', flops='~0.3G'),
    acupoint_head=dict(params='~1.5M', flops='~0.5G'),
    total=dict(params='~30M', flops='~9.3G'),
    
    # 性能目标
    target_performance=dict(
        anchor_pck_0_1=0.95,    # 锚点PCK@0.1
        acupoint_pck_0_05=0.90, # 穴位PCK@0.05
        combined_score=0.92,     # 综合评分
        inference_time='<15ms',  # 推理时间
    ),
)
