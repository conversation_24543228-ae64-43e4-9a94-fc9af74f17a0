# Acu-RTMPOSE简化训练配置
# 基于RTMPose手部关键点检测，适配穴位检测

_base_ = ['../rtmpose/coco_wholebody_hand/rtmpose-m_8xb32-210e_coco-wholebody-hand-256x256.py']

# 运行时配置
max_epochs = 420
val_interval = 10  # 每10个epoch验证一轮
base_lr = 4e-3

# 训练配置
train_cfg = dict(max_epochs=max_epochs, val_interval=val_interval)

# 修改模型配置 - 适配穴位检测
model = dict(
    head=dict(
        out_channels=44,  # 23个穴位 + 21个锚点
        loss=dict(
            type='KLDiscretLoss',
            use_target_weight=True,
            beta=10.,
            label_softmax=True),
        decoder=dict(
            type='SimCCLabel',
            input_size=(256, 192),  # 修改输入尺寸
            sigma=(4.9, 5.66),
            simcc_split_ratio=2.0,
            normalize=False,
            use_dark=False)),
    test_cfg=dict(flip_test=True))

# 数据集配置
dataset_type = 'CocoDataset'
data_mode = 'topdown'
data_root = 'data/acu_rtmpose/'  # 请根据实际数据集路径修改

# 数据管道配置 - 适配256x192输入
train_pipeline = [
    dict(type='LoadImage'),
    dict(type='GetBBoxCenterScale', padding=1.25),
    dict(type='RandomFlip', direction='horizontal'),
    dict(type='RandomHalfBody'),
    dict(
        type='RandomBBoxTransform',
        scale_factor=[0.7, 1.3],
        rotate_factor=60),
    dict(type='TopdownAffine', input_size=(256, 192)),
    dict(type='mmdet.YOLOXHSVRandomAug'),
    dict(
        type='Albumentation',
        transforms=[
            dict(type='Blur', p=0.1),
            dict(type='MedianBlur', p=0.1),
        ]),
    dict(
        type='GenerateTarget',
        encoder=dict(
            type='SimCCLabel',
            input_size=(256, 192),
            sigma=(4.9, 5.66),
            simcc_split_ratio=2.0,
            normalize=False,
            use_dark=False)),
    dict(type='PackPoseInputs')
]

val_pipeline = [
    dict(type='LoadImage'),
    dict(type='GetBBoxCenterScale', padding=1.25),
    dict(type='TopdownAffine', input_size=(256, 192)),
    dict(
        type='GenerateTarget',
        encoder=dict(
            type='SimCCLabel',
            input_size=(256, 192),
            sigma=(4.9, 5.66),
            simcc_split_ratio=2.0,
            normalize=False,
            use_dark=False)),
    dict(type='PackPoseInputs')
]

# 数据加载器配置
train_dataloader = dict(
    batch_size=32,
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        data_mode=data_mode,
        ann_file='train_with_mediapipe.json',
        data_prefix=dict(img='images/'),
        metainfo=dict(
            dataset_name='acu_rtmpose',
            keypoint_info={i: dict(name=f'kpt_{i}', id=i, color=[255, 0, 0]) for i in range(44)},
            joint_weights=[1.0] * 44,
            sigmas=[0.025] * 44),
        pipeline=train_pipeline))

val_dataloader = dict(
    batch_size=32,
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        data_mode=data_mode,
        ann_file='val_with_mediapipe.json',
        data_prefix=dict(img='images/'),
        test_mode=True,
        metainfo=dict(
            dataset_name='acu_rtmpose',
            keypoint_info={i: dict(name=f'kpt_{i}', id=i, color=[255, 0, 0]) for i in range(44)},
            joint_weights=[1.0] * 44,
            sigmas=[0.025] * 44),
        pipeline=val_pipeline))

test_dataloader = val_dataloader

# 评估器配置
val_evaluator = [
    dict(
        type='CocoMetric',
        ann_file=data_root + 'val_with_mediapipe.json',
        use_area=False,
        prefix='acu_rtmpose'),
    dict(
        type='PCKAccuracy',
        thr=0.05,
        norm_item=['bbox', 'torso'],
        prefix='acu_rtmpose')
]
test_evaluator = val_evaluator

# 钩子配置
default_hooks = dict(
    checkpoint=dict(
        save_best='acu_rtmpose/coco/AP',
        rule='greater',
        max_keep_ckpts=1,
        interval=val_interval))

# 工作目录
work_dir = './work_dirs/acu_rtmpose-m_8xb32-420e_simple-256x192'
