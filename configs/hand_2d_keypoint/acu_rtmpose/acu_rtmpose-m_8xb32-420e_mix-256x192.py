# Acu-RTMPOSE完整训练配置
# 层次化穴位检测：锚点解码器 + 信息融合模块 + 穴位解码器

_base_ = ['../../_base_/default_runtime.py']

# 模型配置
model = dict(
    type='TopdownPoseEstimator',  # 暂时使用标准TopdownPoseEstimator
    data_preprocessor=dict(
        type='PoseDataPreprocessor',
        mean=[123.675, 116.28, 103.53],
        std=[58.395, 57.12, 57.375],
        bgr_to_rgb=True),
    backbone=dict(
        _scope_='mmdet',
        type='CSPNeXt',
        arch='P5',
        expand_ratio=0.5,
        deepen_factor=0.67,  # m版本
        widen_factor=0.75,   # m版本
        out_indices=(4, ),
        channel_attention=True,
        norm_cfg=dict(type='SyncBN'),
        act_cfg=dict(type='SiLU'),
        init_cfg=dict(
            type='Pretrained',
            prefix='backbone.',
            checkpoint='https://download.openmmlab.com/mmpose/v1/projects/'
                      'rtmposev1/cspnext-m_udp-aic-coco_210e-256x192-f2f7d6f6_20230130.pth')),
    head=dict(
        type='RTMCCHead',
        in_channels=768,
        out_channels=44,  # 23个穴位 + 21个锚点
        input_size=(256, 192),
        in_featuremap_size=(8, 6),
        simcc_split_ratio=2.0,
        final_layer_kernel_size=7,
        gau_cfg=dict(
            hidden_dims=256,
            s=128,
            expansion_factor=2,
            dropout_rate=0.,
            drop_path=0.,
            act_fn='SiLU',
            use_rel_bias=False,
            pos_enc=False),
        loss=dict(
            type='KLDiscretLoss',
            use_target_weight=True,
            beta=10.,
            label_softmax=True),
        decoder=dict(
            type='SimCCLabel',
            input_size=(256, 192),
            sigma=(4.9, 5.66),
            simcc_split_ratio=2.0,
            normalize=False,
            use_dark=False)),
    test_cfg=dict(flip_test=True))

# 运行时配置
max_epochs = 420
val_interval = 10  # 每10个epoch验证一轮
base_lr = 4e-3

# 训练配置
train_cfg = dict(max_epochs=max_epochs, val_interval=val_interval)
randomness = dict(seed=42)

# 优化器配置 - 分层学习率
optim_wrapper = dict(
    type='OptimWrapper',
    optimizer=dict(
        type='AdamW',
        lr=base_lr,
        betas=(0.9, 0.999),
        weight_decay=0.05),
    paramwise_cfg=dict(
        norm_decay_mult=0,
        bias_decay_mult=0,
        bypass_duplicate=True,
        # 分层学习率设置
        custom_keys={
            'backbone': dict(lr_mult=0.1),      # backbone较低学习率
            'anchor_head': dict(lr_mult=1.0),   # 锚点解码器标准学习率
            'fusion_module': dict(lr_mult=1.0), # 融合模块标准学习率
            'acupoint_head': dict(lr_mult=1.5), # 穴位解码器较高学习率
        }))

# 学习率调度
param_scheduler = [
    dict(
        type='LinearLR',
        start_factor=5e-4,
        by_epoch=False,
        begin=0,
        end=1000),
    dict(
        type='CosineAnnealingLR',
        eta_min=base_lr * 0.01,
        begin=max_epochs // 2,
        end=max_epochs,
        T_max=max_epochs // 2,
        by_epoch=True,
        convert_to_iter_based=True),
]

# 自动缩放学习率
auto_scale_lr = dict(base_batch_size=256)

# 数据集配置
dataset_type = 'CocoDataset'  # 暂时使用标准CocoDataset
data_mode = 'topdown'
data_root = '/root/autodl-tmp/datasets/mix/'

# 数据管道配置
train_pipeline = [
    dict(type='LoadImage'),
    dict(type='GetBBoxCenterScale', padding=1.25),
    dict(type='RandomFlip', direction='horizontal'),
    dict(type='RandomHalfBody'),
    dict(
        type='RandomBBoxTransform',
        scale_factor=[0.7, 1.3],
        rotate_factor=60),
    dict(type='TopdownAffine', input_size=(256, 192)),
    dict(type='mmdet.YOLOXHSVRandomAug'),
    dict(
        type='Albumentation',
        transforms=[
            dict(type='Blur', p=0.1),
            dict(type='MedianBlur', p=0.1),
        ]),
    dict(
        type='GenerateTarget',
        encoder=dict(
            type='SimCCLabel',
            input_size=(256, 192),
            sigma=(4.9, 5.66),
            simcc_split_ratio=2.0,
            normalize=False,
            use_dark=False)),
    dict(type='PackPoseInputs')
]

val_pipeline = [
    dict(type='LoadImage'),
    dict(type='GetBBoxCenterScale', padding=1.25),
    dict(type='TopdownAffine', input_size=(256, 192)),
    dict(
        type='GenerateTarget',
        encoder=dict(
            type='SimCCLabel',
            input_size=(256, 192),
            sigma=(4.9, 5.66),
            simcc_split_ratio=2.0,
            normalize=False,
            use_dark=False)),
    dict(type='PackPoseInputs')
]

# 数据加载器配置
train_dataloader = dict(
    batch_size=32,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=True),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        data_mode=data_mode,
        ann_file='train_with_mediapipe.json',
        data_prefix=dict(img='images/'),
        metainfo=dict(
            dataset_name='acu_rtmpose',
            keypoint_info={i: dict(name=f'kpt_{i}', id=i, color=[255, 0, 0]) for i in range(44)},
            joint_weights=[1.0] * 44,
            sigmas=[0.025] * 44),
        pipeline=train_pipeline))

val_dataloader = dict(
    batch_size=32,
    num_workers=4,
    persistent_workers=True,
    drop_last=False,
    sampler=dict(type='DefaultSampler', shuffle=False, round_up=False),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        data_mode=data_mode,
        ann_file='val_with_mediapipe.json',
        data_prefix=dict(img='images/'),
        test_mode=True,
        metainfo=dict(from_file='configs/_base_/datasets/acu_rtmpose.py'),
        pipeline=val_pipeline))

test_dataloader = val_dataloader

# 评估器配置
val_evaluator = [
    dict(
        type='CocoMetric',
        ann_file=data_root + 'val_with_mediapipe.json',
        use_area=False,
        prefix='acu_rtmpose'),
    dict(
        type='PCKAccuracy',
        thr=0.05,
        norm_item=['bbox', 'torso'],
        prefix='acu_rtmpose')
]
test_evaluator = val_evaluator

# 钩子配置
default_hooks = dict(
    checkpoint=dict(
        type='CheckpointHook',
        save_best='acu_rtmpose/coco/AP',
        rule='greater',
        max_keep_ckpts=1,
        interval=val_interval),
    logger=dict(type='LoggerHook', interval=50),
    param_scheduler=dict(type='ParamSchedulerHook'),
    timer=dict(type='IterTimerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    visualization=dict(
        type='PoseVisualizationHook',
        enable=False,
        interval=1,
        kpt_thr=0.3))

# 自定义钩子
custom_hooks = [
    dict(
        type='EMAHook',
        ema_type='ExpMomentumEMA',
        momentum=0.0002,
        update_buffers=True,
        priority=49),
    dict(
        type='mmdet.PipelineSwitchHook',
        switch_epoch=max_epochs - 10,
        switch_pipeline=[])
]

# 可视化配置
vis_backends = [
    dict(type='LocalVisBackend'),
    dict(type='TensorboardVisBackend'),
]
visualizer = dict(
    type='PoseLocalVisualizer',
    vis_backends=vis_backends,
    name='visualizer')

# 日志配置
log_processor = dict(
    type='LogProcessor',
    window_size=50,
    by_epoch=True,
    num_digits=6)
log_level = 'INFO'

# 加载和恢复
load_from = None
resume = False

# 工作目录
work_dir = './work_dirs/acu_rtmpose-m_8xb32-420e_mix-256x192'
