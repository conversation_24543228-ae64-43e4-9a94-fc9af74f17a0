# 2D Hand Pose Estimation

2D hand pose estimation is defined as the task of detecting the poses (or keypoints) of the hand from an input image.

Normally, the input images are cropped hand images, where the hand locates at the center;
or the rough location (or the bounding box) of the hand is provided.

## Data preparation

Please follow [DATA Preparation](/docs/en/dataset_zoo/2d_hand_keypoint.md) to prepare data.

## Demo

Please follow [Demo](/demo/docs/en/2d_hand_demo.md) to run demos.

<img src="https://user-images.githubusercontent.com/11788150/109098558-8c54db00-775c-11eb-8966-85df96b23dc5.gif" width="600px" alt><br>

<img src="https://user-images.githubusercontent.com/26127467/187664103-cfbe0c4e-5876-42f9-9023-5fb58ce00d7b.jpg" height="500px" alt><br>
