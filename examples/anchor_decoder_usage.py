#!/usr/bin/env python3
"""
Acu-RTMPOSE锚点解码器使用示例
演示如何使用锚点解码器进行MediaPipe手部骨骼锚点检测
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Tuple, List
import matplotlib.pyplot as plt

# 假设已经正确安装和配置了mmpose
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

def create_anchor_decoder():
    """创建锚点解码器实例"""
    from mmpose.models.heads.coord_cls_heads import AnchorDecoder
    
    anchor_decoder = AnchorDecoder(
        in_channels=768,  # CSPNeXt-m输出通道
        out_channels=21,  # 21个MediaPipe锚点
        input_size=(256, 192),
        in_featuremap_size=(8, 6),
        simcc_split_ratio=2.0,
        final_layer_kernel_size=7,
        gau_cfg=dict(
            hidden_dims=256,
            s=128,
            expansion_factor=2,
            dropout_rate=0.0,
            drop_path=0.0,
            act_fn='SiLU',
            use_rel_bias=False,
            pos_enc=False),
        loss=dict(type='KLDiscretLoss', use_target_weight=True),
    )
    
    return anchor_decoder

def create_mock_backbone():
    """创建模拟的backbone网络"""
    class MockBackbone(nn.Module):
        def __init__(self):
            super().__init__()
            # 模拟CSPNeXt-m的最后几层
            self.conv_layers = nn.Sequential(
                nn.Conv2d(3, 64, 3, 2, 1),    # 256x192 -> 128x96
                nn.BatchNorm2d(64),
                nn.SiLU(),
                nn.Conv2d(64, 128, 3, 2, 1),  # 128x96 -> 64x48
                nn.BatchNorm2d(128),
                nn.SiLU(),
                nn.Conv2d(128, 256, 3, 2, 1), # 64x48 -> 32x24
                nn.BatchNorm2d(256),
                nn.SiLU(),
                nn.Conv2d(256, 512, 3, 2, 1), # 32x24 -> 16x12
                nn.BatchNorm2d(512),
                nn.SiLU(),
                nn.Conv2d(512, 768, 3, 2, 1), # 16x12 -> 8x6
                nn.BatchNorm2d(768),
                nn.SiLU(),
            )
        
        def forward(self, x):
            return self.conv_layers(x)
    
    return MockBackbone()

def create_complete_model():
    """创建完整的锚点检测模型"""
    class AnchorDetectionModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.backbone = create_mock_backbone()
            self.anchor_decoder = create_anchor_decoder()
        
        def forward(self, x):
            # Backbone特征提取
            features = self.backbone(x)
            
            # 锚点解码
            pred_x, pred_y = self.anchor_decoder((features,))
            
            return pred_x, pred_y
        
        def predict_keypoints(self, x):
            """预测关键点坐标"""
            pred_x, pred_y = self.forward(x)
            
            # 从SimCC预测中解码坐标
            batch_size, num_keypoints = pred_x.shape[:2]
            keypoints = torch.zeros(batch_size, num_keypoints, 2)
            
            for b in range(batch_size):
                for k in range(num_keypoints):
                    # 找到最大值位置作为坐标
                    x_coord = torch.argmax(pred_x[b, k]).float() / 2.0  # 除以split_ratio
                    y_coord = torch.argmax(pred_y[b, k]).float() / 2.0
                    
                    keypoints[b, k, 0] = x_coord
                    keypoints[b, k, 1] = y_coord
            
            return keypoints
    
    return AnchorDetectionModel()

def visualize_anchor_predictions(image: np.ndarray, 
                                keypoints: np.ndarray,
                                save_path: str = None):
    """可视化锚点预测结果"""
    # MediaPipe手部关键点连接关系
    connections = [
        # 拇指
        (0, 1), (1, 2), (2, 3), (3, 4),
        # 食指
        (0, 5), (5, 6), (6, 7), (7, 8),
        # 中指
        (0, 9), (9, 10), (10, 11), (11, 12),
        # 无名指
        (0, 13), (13, 14), (14, 15), (15, 16),
        # 小指
        (0, 17), (17, 18), (18, 19), (19, 20),
    ]
    
    plt.figure(figsize=(10, 8))
    plt.imshow(image)
    
    # 绘制关键点
    for i, (x, y) in enumerate(keypoints):
        plt.plot(x, y, 'ro', markersize=8)
        plt.text(x+2, y+2, str(i), fontsize=8, color='white', 
                bbox=dict(boxstyle="round,pad=0.3", facecolor='red', alpha=0.7))
    
    # 绘制连接线
    for start, end in connections:
        if start < len(keypoints) and end < len(keypoints):
            x1, y1 = keypoints[start]
            x2, y2 = keypoints[end]
            plt.plot([x1, x2], [y1, y2], 'b-', linewidth=2, alpha=0.7)
    
    plt.title('MediaPipe Hand Landmarks (Anchor Points)')
    plt.axis('off')
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"可视化结果保存到: {save_path}")
    
    plt.show()

def demo_anchor_detection():
    """演示锚点检测的完整流程"""
    print("🚀 Acu-RTMPOSE锚点解码器演示")
    print("=" * 50)
    
    # 1. 创建模型
    print("1. 创建锚点检测模型...")
    model = create_complete_model()
    model.eval()
    
    # 统计参数
    total_params = sum(p.numel() for p in model.parameters())
    anchor_params = sum(p.numel() for p in model.anchor_decoder.parameters())
    print(f"   - 总参数数: {total_params:,}")
    print(f"   - 锚点解码器参数: {anchor_params:,}")
    print(f"   - 锚点解码器占比: {anchor_params/total_params*100:.1f}%")
    
    # 2. 创建模拟输入
    print("\n2. 创建模拟输入...")
    batch_size = 2
    input_tensor = torch.randn(batch_size, 3, 192, 256)
    print(f"   - 输入形状: {input_tensor.shape}")
    
    # 3. 前向推理
    print("\n3. 执行前向推理...")
    with torch.no_grad():
        pred_x, pred_y = model(input_tensor)
        keypoints = model.predict_keypoints(input_tensor)
    
    print(f"   - X预测形状: {pred_x.shape}")
    print(f"   - Y预测形状: {pred_y.shape}")
    print(f"   - 关键点形状: {keypoints.shape}")
    
    # 4. 分析预测结果
    print("\n4. 分析预测结果...")
    for b in range(batch_size):
        kpts = keypoints[b].numpy()
        print(f"   - 样本{b+1}:")
        print(f"     * 手腕位置: ({kpts[0, 0]:.1f}, {kpts[0, 1]:.1f})")
        print(f"     * 拇指尖: ({kpts[4, 0]:.1f}, {kpts[4, 1]:.1f})")
        print(f"     * 食指尖: ({kpts[8, 0]:.1f}, {kpts[8, 1]:.1f})")
        print(f"     * 中指尖: ({kpts[12, 0]:.1f}, {kpts[12, 1]:.1f})")
        print(f"     * 无名指尖: ({kpts[16, 0]:.1f}, {kpts[16, 1]:.1f})")
        print(f"     * 小指尖: ({kpts[20, 0]:.1f}, {kpts[20, 1]:.1f})")
    
    # 5. 性能测试
    print("\n5. 性能测试...")
    import time
    
    # 预热
    for _ in range(10):
        with torch.no_grad():
            _ = model(input_tensor)
    
    # 测试推理时间
    start_time = time.time()
    num_runs = 100
    for _ in range(num_runs):
        with torch.no_grad():
            _ = model(input_tensor)
    end_time = time.time()
    
    avg_time = (end_time - start_time) / num_runs * 1000  # 转换为毫秒
    fps = 1000 / avg_time
    
    print(f"   - 平均推理时间: {avg_time:.2f} ms")
    print(f"   - 推理FPS: {fps:.1f}")
    
    # 6. 创建可视化示例
    print("\n6. 创建可视化示例...")
    # 创建模拟图像
    demo_image = np.random.randint(0, 255, (192, 256, 3), dtype=np.uint8)
    demo_keypoints = keypoints[0].numpy()
    
    # 可视化（如果在支持显示的环境中）
    try:
        visualize_anchor_predictions(demo_image, demo_keypoints, 'anchor_demo.png')
    except Exception as e:
        print(f"   - 可视化跳过（显示环境不支持）: {e}")
    
    print("\n✅ 锚点解码器演示完成！")

def benchmark_anchor_decoder():
    """锚点解码器性能基准测试"""
    print("\n🔬 锚点解码器性能基准测试")
    print("=" * 50)
    
    # 测试不同配置
    configs = [
        {'hidden_dims': 128, 'name': '轻量级'},
        {'hidden_dims': 256, 'name': '标准'},
        {'hidden_dims': 512, 'name': '高性能'},
    ]
    
    for config in configs:
        print(f"\n测试配置: {config['name']}")
        
        # 创建解码器
        from mmpose.models.heads.coord_cls_heads import AnchorDecoder
        decoder = AnchorDecoder(
            in_channels=768,
            out_channels=21,
            input_size=(256, 192),
            in_featuremap_size=(8, 6),
            gau_cfg=dict(
                hidden_dims=config['hidden_dims'],
                s=128,
                expansion_factor=2,
                dropout_rate=0.0,
                drop_path=0.0,
                act_fn='SiLU',
                use_rel_bias=False,
                pos_enc=False),
        )
        
        # 统计参数
        params = sum(p.numel() for p in decoder.parameters())
        
        # 测试推理时间
        input_feat = torch.randn(1, 768, 6, 8)
        
        # 预热
        decoder.eval()
        for _ in range(10):
            with torch.no_grad():
                _ = decoder((input_feat,))
        
        # 计时
        import time
        start = time.time()
        for _ in range(100):
            with torch.no_grad():
                _ = decoder((input_feat,))
        end = time.time()
        
        avg_time = (end - start) / 100 * 1000
        
        print(f"   - 参数数量: {params:,}")
        print(f"   - 推理时间: {avg_time:.2f} ms")
        print(f"   - 内存占用: {params * 4 / 1024 / 1024:.2f} MB")

if __name__ == "__main__":
    # 运行演示
    demo_anchor_detection()
    
    # 运行基准测试
    benchmark_anchor_decoder()
    
    print("\n🎉 所有演示完成！")
