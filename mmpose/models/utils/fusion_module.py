# Copyright (c) OpenMMLab. All rights reserved.
"""
Fusion Module for Acu-RTMPOSE
信息融合模块 - 将锚点信息融合到backbone特征中，为穴位检测提供增强的特征表示
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional, List
import numpy as np

from mmpose.registry import MODELS


@MODELS.register_module()
class AnchorFeatureFusion(nn.Module):
    """锚点特征融合模块
    
    将锚点解码器的输出信息融合到backbone特征中，生成增强的特征表示。
    
    融合策略：
    1. 锚点位置编码：将锚点坐标转换为位置编码
    2. 注意力机制：使用锚点信息指导特征注意力
    3. 特征增强：将锚点特征与backbone特征融合
    
    Args:
        backbone_channels (int): Backbone特征通道数
        anchor_channels (int): 锚点特征通道数，默认256
        fusion_channels (int): 融合后特征通道数
        num_anchors (int): 锚点数量，默认21
        fusion_type (str): 融合类型，'attention'|'concat'|'add'
        use_position_encoding (bool): 是否使用位置编码
        dropout_rate (float): Dropout比例
    """
    
    def __init__(self,
                 backbone_channels: int = 768,
                 anchor_channels: int = 256,
                 fusion_channels: int = 768,
                 num_anchors: int = 21,
                 fusion_type: str = 'attention',
                 use_position_encoding: bool = True,
                 dropout_rate: float = 0.1):
        super().__init__()
        
        self.backbone_channels = backbone_channels
        self.anchor_channels = anchor_channels
        self.fusion_channels = fusion_channels
        self.num_anchors = num_anchors
        self.fusion_type = fusion_type
        self.use_position_encoding = use_position_encoding
        
        # 锚点特征处理
        self.anchor_feature_proj = nn.Sequential(
            nn.Linear(2, anchor_channels // 4),  # 坐标 -> 特征
            nn.ReLU(inplace=True),
            nn.Linear(anchor_channels // 4, anchor_channels // 2),
            nn.ReLU(inplace=True),
            nn.Linear(anchor_channels // 2, anchor_channels),
            nn.Dropout(dropout_rate)
        )
        
        # 位置编码
        if use_position_encoding:
            self.position_encoding = PositionalEncoding2D(anchor_channels)
        
        # 不同的融合策略
        if fusion_type == 'attention':
            self._build_attention_fusion()
        elif fusion_type == 'concat':
            self._build_concat_fusion()
        elif fusion_type == 'add':
            self._build_add_fusion()
        else:
            raise ValueError(f"Unsupported fusion_type: {fusion_type}")
        
        # 输出投影
        self.output_proj = nn.Sequential(
            nn.Conv2d(fusion_channels, fusion_channels, 1),
            nn.BatchNorm2d(fusion_channels),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout_rate)
        )
        
        self._init_weights()
    
    def _build_attention_fusion(self):
        """构建注意力融合模块"""
        # 交叉注意力：backbone特征作为query，锚点特征作为key和value
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=self.backbone_channels,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        # 特征对齐
        self.anchor_key_proj = nn.Linear(self.anchor_channels, self.backbone_channels)
        self.anchor_value_proj = nn.Linear(self.anchor_channels, self.backbone_channels)
        
        # 融合后处理
        self.fusion_norm = nn.LayerNorm(self.backbone_channels)
        self.fusion_ffn = nn.Sequential(
            nn.Linear(self.backbone_channels, self.backbone_channels * 2),
            nn.ReLU(inplace=True),
            nn.Linear(self.backbone_channels * 2, self.fusion_channels),
            nn.Dropout(0.1)
        )
    
    def _build_concat_fusion(self):
        """构建拼接融合模块"""
        # 锚点特征空间对齐
        self.anchor_spatial_proj = nn.Sequential(
            nn.Linear(self.anchor_channels, self.backbone_channels),
            nn.ReLU(inplace=True)
        )
        
        # 拼接后的通道压缩
        concat_channels = self.backbone_channels * 2  # backbone + anchor
        self.concat_proj = nn.Sequential(
            nn.Conv2d(concat_channels, self.fusion_channels, 1),
            nn.BatchNorm2d(self.fusion_channels),
            nn.ReLU(inplace=True)
        )
    
    def _build_add_fusion(self):
        """构建加法融合模块"""
        # 锚点特征对齐到backbone维度
        self.anchor_align = nn.Sequential(
            nn.Linear(self.anchor_channels, self.backbone_channels),
            nn.ReLU(inplace=True)
        )
        
        # 加权融合
        self.fusion_weight = nn.Parameter(torch.tensor(0.5))
        
        # 输出投影
        if self.fusion_channels != self.backbone_channels:
            self.channel_proj = nn.Conv2d(self.backbone_channels, self.fusion_channels, 1)
        else:
            self.channel_proj = nn.Identity()
    
    def _init_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.BatchNorm2d, nn.LayerNorm)):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, 
                backbone_features: torch.Tensor,
                anchor_coords: torch.Tensor,
                anchor_confidence: Optional[torch.Tensor] = None) -> torch.Tensor:
        """前向传播
        
        Args:
            backbone_features: Backbone特征 [B, C, H, W]
            anchor_coords: 锚点坐标 [B, num_anchors, 2]
            anchor_confidence: 锚点置信度 [B, num_anchors] (可选)
            
        Returns:
            torch.Tensor: 融合后的特征 [B, fusion_channels, H, W]
        """
        B, C, H, W = backbone_features.shape
        
        # 1. 处理锚点特征
        anchor_features = self.anchor_feature_proj(anchor_coords)  # [B, num_anchors, anchor_channels]
        
        # 2. 位置编码
        if self.use_position_encoding:
            pos_encoding = self.position_encoding(H, W, device=anchor_coords.device)
            # 将锚点坐标映射到特征图坐标
            anchor_coords_norm = self._normalize_coords(anchor_coords, H, W)
            anchor_pos_features = self._sample_position_encoding(pos_encoding, anchor_coords_norm)
            anchor_features = anchor_features + anchor_pos_features
        
        # 3. 应用置信度权重
        if anchor_confidence is not None:
            anchor_features = anchor_features * anchor_confidence.unsqueeze(-1)
        
        # 4. 特征融合
        if self.fusion_type == 'attention':
            fused_features = self._attention_fusion(backbone_features, anchor_features)
        elif self.fusion_type == 'concat':
            fused_features = self._concat_fusion(backbone_features, anchor_features, H, W)
        elif self.fusion_type == 'add':
            fused_features = self._add_fusion(backbone_features, anchor_features, H, W)
        
        # 5. 输出投影
        output = self.output_proj(fused_features)
        
        return output
    
    def _normalize_coords(self, coords: torch.Tensor, H: int, W: int) -> torch.Tensor:
        """将像素坐标归一化到[-1, 1]范围"""
        coords_norm = coords.clone()
        coords_norm[..., 0] = 2.0 * coords[..., 0] / W - 1.0  # x
        coords_norm[..., 1] = 2.0 * coords[..., 1] / H - 1.0  # y
        return coords_norm
    
    def _sample_position_encoding(self, pos_encoding: torch.Tensor, coords_norm: torch.Tensor) -> torch.Tensor:
        """从位置编码中采样锚点位置的编码"""
        # pos_encoding: [1, C, H, W]
        # coords_norm: [B, num_anchors, 2] in [-1, 1]
        B, num_anchors = coords_norm.shape[:2]
        
        # 使用grid_sample进行双线性插值采样
        coords_grid = coords_norm.unsqueeze(2)  # [B, num_anchors, 1, 2]
        
        # 扩展位置编码到batch维度
        pos_encoding_batch = pos_encoding.expand(B, -1, -1, -1)
        
        # 采样位置编码
        sampled_pos = F.grid_sample(
            pos_encoding_batch, coords_grid, 
            mode='bilinear', padding_mode='border', align_corners=False
        )  # [B, C, num_anchors, 1]
        
        sampled_pos = sampled_pos.squeeze(-1).transpose(1, 2)  # [B, num_anchors, C]
        
        return sampled_pos
    
    def _attention_fusion(self, backbone_features: torch.Tensor, anchor_features: torch.Tensor) -> torch.Tensor:
        """注意力融合"""
        B, C, H, W = backbone_features.shape
        
        # 将backbone特征reshape为序列
        backbone_seq = backbone_features.flatten(2).transpose(1, 2)  # [B, H*W, C]
        
        # 准备注意力的key和value
        anchor_keys = self.anchor_key_proj(anchor_features)  # [B, num_anchors, C]
        anchor_values = self.anchor_value_proj(anchor_features)  # [B, num_anchors, C]
        
        # 交叉注意力
        attended_features, _ = self.cross_attention(
            query=backbone_seq,
            key=anchor_keys,
            value=anchor_values
        )  # [B, H*W, C]
        
        # 残差连接和归一化
        attended_features = self.fusion_norm(attended_features + backbone_seq)
        
        # FFN
        fused_features = self.fusion_ffn(attended_features)  # [B, H*W, fusion_channels]
        
        # Reshape回特征图格式
        fused_features = fused_features.transpose(1, 2).reshape(B, self.fusion_channels, H, W)
        
        return fused_features
    
    def _concat_fusion(self, backbone_features: torch.Tensor, anchor_features: torch.Tensor, H: int, W: int) -> torch.Tensor:
        """拼接融合"""
        B = backbone_features.shape[0]

        # 将锚点特征投影到空间
        anchor_spatial = self.anchor_spatial_proj(anchor_features)  # [B, num_anchors, C]

        # 创建锚点特征图（简化版本：全局平均）
        anchor_global = anchor_spatial.mean(dim=1)  # [B, C]
        # 正确的维度扩展：[B, C] -> [B, C, H, W]
        anchor_feature_map = anchor_global.unsqueeze(-1).unsqueeze(-1).expand(B, self.backbone_channels, H, W)

        # 拼接特征
        concat_features = torch.cat([backbone_features, anchor_feature_map], dim=1)

        # 投影到目标维度
        fused_features = self.concat_proj(concat_features)

        return fused_features
    
    def _add_fusion(self, backbone_features: torch.Tensor, anchor_features: torch.Tensor, H: int, W: int) -> torch.Tensor:
        """加法融合"""
        B = backbone_features.shape[0]

        # 对齐锚点特征维度
        anchor_aligned = self.anchor_align(anchor_features)  # [B, num_anchors, backbone_channels]

        # 全局平均池化锚点特征
        anchor_global = anchor_aligned.mean(dim=1)  # [B, backbone_channels]
        # 正确的维度扩展：[B, C] -> [B, C, H, W]
        anchor_feature_map = anchor_global.unsqueeze(-1).unsqueeze(-1).expand(B, self.backbone_channels, H, W)

        # 加权融合
        fused_features = (1 - self.fusion_weight) * backbone_features + self.fusion_weight * anchor_feature_map

        # 通道投影
        fused_features = self.channel_proj(fused_features)

        return fused_features


class PositionalEncoding2D(nn.Module):
    """2D位置编码"""
    
    def __init__(self, channels: int, temperature: int = 10000):
        super().__init__()
        self.channels = channels
        self.temperature = temperature
        
    def forward(self, H: int, W: int, device: torch.device) -> torch.Tensor:
        """生成2D位置编码
        
        Args:
            H: 特征图高度
            W: 特征图宽度
            device: 设备
            
        Returns:
            torch.Tensor: 位置编码 [1, channels, H, W]
        """
        y_embed = torch.arange(H, dtype=torch.float32, device=device).unsqueeze(1).repeat(1, W)
        x_embed = torch.arange(W, dtype=torch.float32, device=device).unsqueeze(0).repeat(H, 1)
        
        # 归一化到[0, 1]
        y_embed = y_embed / H
        x_embed = x_embed / W
        
        dim_t = torch.arange(self.channels // 2, dtype=torch.float32, device=device)
        dim_t = self.temperature ** (2 * dim_t / self.channels)
        
        pos_x = x_embed.unsqueeze(-1) / dim_t
        pos_y = y_embed.unsqueeze(-1) / dim_t
        
        pos_x = torch.stack((pos_x[..., 0::2].sin(), pos_x[..., 1::2].cos()), dim=-1).flatten(-2)
        pos_y = torch.stack((pos_y[..., 0::2].sin(), pos_y[..., 1::2].cos()), dim=-1).flatten(-2)
        
        pos = torch.cat((pos_y, pos_x), dim=-1).permute(2, 0, 1).unsqueeze(0)
        
        return pos
