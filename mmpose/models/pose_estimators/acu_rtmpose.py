# Copyright (c) OpenMMLab. All rights reserved.
"""
Acu-RTMPOSE: 层次化穴位检测模型
结合锚点解码器和信息融合模块的完整实现
"""

from typing import Dict, List, Optional, Tuple, Union
import torch
import torch.nn as nn

from mmpose.registry import MODELS
from mmpose.utils.typing import (ConfigType, InstanceList, OptConfigType,
                                 OptSampleList, SampleList)
from .topdown import TopdownPoseEstimator


@MODELS.register_module()
class AcuRTMPose(TopdownPoseEstimator):
    """Acu-RTMPOSE: 层次化穴位检测模型
    
    两阶段层次化架构：
    1. 第一阶段：锚点解码器检测21个MediaPipe骨骼锚点
    2. 信息融合：将锚点信息融合到backbone特征中
    3. 第二阶段：穴位解码器检测23个中医穴位
    
    Args:
        backbone (dict): Backbone配置
        anchor_head (dict): 锚点解码器配置
        fusion_module (dict): 信息融合模块配置
        acupoint_head (dict): 穴位解码器配置 (可选，用于完整的两阶段)
        neck (dict, optional): Neck配置
        train_cfg (dict, optional): 训练配置
        test_cfg (dict, optional): 测试配置
        data_preprocessor (dict, optional): 数据预处理器配置
        init_cfg (dict, optional): 初始化配置
    """

    def __init__(self,
                 backbone: ConfigType,
                 anchor_head: ConfigType,
                 fusion_module: ConfigType,
                 acupoint_head: Optional[ConfigType] = None,
                 neck: Optional[ConfigType] = None,
                 train_cfg: Optional[ConfigType] = None,
                 test_cfg: Optional[ConfigType] = None,
                 data_preprocessor: Optional[ConfigType] = None,
                 init_cfg: Optional[ConfigType] = None):

        super().__init__(
            backbone=backbone,
            head=anchor_head,  # 暂时使用anchor_head作为主head
            neck=neck,
            train_cfg=train_cfg,
            test_cfg=test_cfg,
            data_preprocessor=data_preprocessor,
            init_cfg=init_cfg)

        # 重命名head为anchor_head以避免混淆
        self.anchor_head = self.head
        delattr(self, 'head')

        # 构建信息融合模块
        self.fusion_module = MODELS.build(fusion_module)

        # 构建穴位解码器（如果提供）
        if acupoint_head is not None:
            self.acupoint_head = MODELS.build(acupoint_head)
        else:
            self.acupoint_head = None

        # 标记是否为两阶段模式
        self.two_stage_mode = acupoint_head is not None

        # 设置训练阶段（用于分阶段训练）
        self.training_stage = 'full'  # 'anchor_only', 'joint', 'full'

    def extract_feat(self, inputs: torch.Tensor) -> Tuple[torch.Tensor]:
        """提取特征
        
        Args:
            inputs: 输入图像 [B, 3, H, W]
            
        Returns:
            Tuple[torch.Tensor]: 特征元组
        """
        x = self.backbone(inputs)
        if self.with_neck:
            x = self.neck(x)
        return x

    def forward(self,
                inputs: torch.Tensor,
                data_samples: OptSampleList = None,
                mode: str = 'tensor') -> Union[Dict[str, torch.Tensor], list]:
        """前向传播
        
        Args:
            inputs: 输入张量
            data_samples: 数据样本
            mode: 前向模式 ('tensor', 'predict', 'loss')
            
        Returns:
            根据mode返回不同格式的结果
        """
        if mode == 'loss':
            return self.loss(inputs, data_samples)
        elif mode == 'predict':
            return self.predict(inputs, data_samples)
        else:
            return self._forward(inputs)

    def _forward(self, inputs: torch.Tensor) -> Dict[str, torch.Tensor]:
        """纯前向传播（tensor模式）
        
        Args:
            inputs: 输入张量 [B, 3, H, W]
            
        Returns:
            Dict[str, torch.Tensor]: 包含各阶段输出的字典
        """
        # 1. 特征提取
        backbone_features = self.extract_feat(inputs)

        # 2. 第一阶段：锚点检测
        anchor_pred_x, anchor_pred_y = self.anchor_head(backbone_features)

        # 3. 从锚点预测中解码坐标
        anchor_coords = self._decode_anchor_coords(anchor_pred_x, anchor_pred_y)

        # 4. 信息融合
        fused_features = self.fusion_module(
            backbone_features=backbone_features[-1],  # 使用最后一层特征
            anchor_coords=anchor_coords
        )

        results = {
            'anchor_pred_x': anchor_pred_x,
            'anchor_pred_y': anchor_pred_y,
            'anchor_coords': anchor_coords,
            'fused_features': fused_features,
        }

        # 5. 第二阶段：穴位检测（如果有穴位解码器）
        if self.two_stage_mode:
            # 将融合特征包装为元组格式
            fused_features_tuple = (fused_features,)
            acupoint_pred_x, acupoint_pred_y = self.acupoint_head(fused_features_tuple)
            
            results.update({
                'acupoint_pred_x': acupoint_pred_x,
                'acupoint_pred_y': acupoint_pred_y,
            })

        return results

    def _decode_anchor_coords(self, pred_x: torch.Tensor, pred_y: torch.Tensor) -> torch.Tensor:
        """从SimCC预测中解码锚点坐标
        
        Args:
            pred_x: X坐标预测 [B, 21, W*split_ratio]
            pred_y: Y坐标预测 [B, 21, H*split_ratio]
            
        Returns:
            torch.Tensor: 锚点坐标 [B, 21, 2]
        """
        B, num_anchors = pred_x.shape[:2]
        
        # 找到最大值位置
        x_indices = torch.argmax(pred_x, dim=-1).float()  # [B, 21]
        y_indices = torch.argmax(pred_y, dim=-1).float()  # [B, 21]
        
        # 转换为原始坐标（除以split_ratio）
        split_ratio = 2.0  # 默认值，应该从配置中获取
        x_coords = x_indices / split_ratio
        y_coords = y_indices / split_ratio
        
        # 组合坐标
        coords = torch.stack([x_coords, y_coords], dim=-1)  # [B, 21, 2]
        
        return coords

    def loss(self, inputs: torch.Tensor, data_samples: SampleList) -> dict:
        """计算损失
        
        Args:
            inputs: 输入张量
            data_samples: 数据样本列表
            
        Returns:
            dict: 损失字典
        """
        # 前向传播
        outputs = self._forward(inputs)

        # 计算锚点损失
        anchor_losses = self.anchor_head.loss(
            feats=self.extract_feat(inputs),
            batch_data_samples=data_samples,
            train_cfg=self.train_cfg
        )

        losses = {}
        # 添加前缀以区分不同阶段的损失
        for key, value in anchor_losses.items():
            losses[f'anchor_{key}'] = value

        # 如果是两阶段模式，计算穴位损失
        if self.two_stage_mode:
            # 准备穴位数据样本（需要从原始数据样本中提取穴位标签）
            acupoint_data_samples = self._prepare_acupoint_data_samples(data_samples)
            
            # 使用融合特征计算穴位损失
            fused_features_tuple = (outputs['fused_features'],)
            acupoint_losses = self.acupoint_head.loss(
                feats=fused_features_tuple,
                batch_data_samples=acupoint_data_samples,
                train_cfg=self.train_cfg
            )
            
            # 添加穴位损失
            for key, value in acupoint_losses.items():
                losses[f'acupoint_{key}'] = value

        return losses

    def predict(self, inputs: torch.Tensor, data_samples: SampleList) -> InstanceList:
        """预测
        
        Args:
            inputs: 输入张量
            data_samples: 数据样本列表
            
        Returns:
            InstanceList: 预测结果列表
        """
        # 前向传播
        outputs = self._forward(inputs)

        # 锚点预测
        anchor_preds = self.anchor_head.predict(
            feats=self.extract_feat(inputs),
            batch_data_samples=data_samples,
            test_cfg=self.test_cfg
        )

        # 如果是两阶段模式，进行穴位预测
        if self.two_stage_mode:
            # 准备穴位数据样本
            acupoint_data_samples = self._prepare_acupoint_data_samples(data_samples)
            
            # 穴位预测
            fused_features_tuple = (outputs['fused_features'],)
            acupoint_preds = self.acupoint_head.predict(
                feats=fused_features_tuple,
                batch_data_samples=acupoint_data_samples,
                test_cfg=self.test_cfg
            )
            
            # 合并预测结果
            combined_preds = self._combine_predictions(anchor_preds, acupoint_preds)
            return combined_preds
        else:
            return anchor_preds

    def _prepare_acupoint_data_samples(self, data_samples: SampleList) -> SampleList:
        """准备穴位数据样本
        
        从原始数据样本中提取穴位相关的标签信息
        
        Args:
            data_samples: 原始数据样本
            
        Returns:
            SampleList: 穴位数据样本
        """
        acupoint_data_samples = []
        
        for data_sample in data_samples:
            # 创建新的数据样本，只包含穴位相关信息
            acupoint_sample = type(data_sample)()
            
            # 复制元信息
            if hasattr(data_sample, 'metainfo'):
                acupoint_sample.metainfo = data_sample.metainfo.copy()
            
            # 提取穴位标签（如果存在）
            if hasattr(data_sample, 'gt_instances'):
                gt_instances = type(data_sample.gt_instances)()
                
                # 复制穴位相关的标签
                for attr in ['acupoint_simcc_x', 'acupoint_simcc_y', 'acupoint_weights']:
                    if hasattr(data_sample.gt_instances, attr):
                        setattr(gt_instances, attr, getattr(data_sample.gt_instances, attr))
                
                acupoint_sample.gt_instances = gt_instances
            
            acupoint_data_samples.append(acupoint_sample)
        
        return acupoint_data_samples

    def _combine_predictions(self, anchor_preds: InstanceList, acupoint_preds: InstanceList) -> InstanceList:
        """合并锚点和穴位预测结果
        
        Args:
            anchor_preds: 锚点预测结果
            acupoint_preds: 穴位预测结果
            
        Returns:
            InstanceList: 合并后的预测结果
        """
        combined_preds = []
        
        for anchor_pred, acupoint_pred in zip(anchor_preds, acupoint_preds):
            # 创建合并的预测结果
            combined_pred = type(anchor_pred)()
            
            # 合并关键点（锚点 + 穴位）
            if hasattr(anchor_pred, 'keypoints') and hasattr(acupoint_pred, 'keypoints'):
                import numpy as np
                anchor_kpts = anchor_pred.keypoints
                acupoint_kpts = acupoint_pred.keypoints
                
                # 拼接关键点 [穴位(23) + 锚点(21) = 44]
                combined_kpts = np.concatenate([acupoint_kpts, anchor_kpts], axis=1)
                combined_pred.keypoints = combined_kpts
            
            # 合并置信度分数
            if hasattr(anchor_pred, 'keypoint_scores') and hasattr(acupoint_pred, 'keypoint_scores'):
                anchor_scores = anchor_pred.keypoint_scores
                acupoint_scores = acupoint_pred.keypoint_scores
                
                combined_scores = np.concatenate([acupoint_scores, anchor_scores], axis=1)
                combined_pred.keypoint_scores = combined_scores
            
            # 复制其他属性
            for attr in ['bbox_scores', 'bboxes']:
                if hasattr(anchor_pred, attr):
                    setattr(combined_pred, attr, getattr(anchor_pred, attr))
            
            combined_preds.append(combined_pred)
        
        return combined_preds

    @property
    def with_neck(self) -> bool:
        """是否有neck"""
        return hasattr(self, 'neck') and self.neck is not None

    @property
    def with_anchor_head(self) -> bool:
        """是否有锚点解码器"""
        return hasattr(self, 'anchor_head') and self.anchor_head is not None

    @property
    def with_acupoint_head(self) -> bool:
        """是否有穴位解码器"""
        return hasattr(self, 'acupoint_head') and self.acupoint_head is not None
