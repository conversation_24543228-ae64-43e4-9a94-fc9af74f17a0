# Copyright (c) OpenMMLab. All rights reserved.
"""
Acupoint Decoder for Acu-RTMPOSE
穴位解码器 - Acu-RTMPOSE的第二阶段解码器，用于检测23个中医穴位
"""

import warnings
from typing import Optional, Sequence, Tuple, Union

import torch
from mmengine.dist import get_dist_info
from mmengine.structures import PixelData
from torch import Tensor, nn

from mmpose.codecs.utils import get_simcc_normalized
from mmpose.evaluation.functional import simcc_pck_accuracy
from mmpose.models.utils.rtmcc_block import RTMCCBlock, ScaleNorm
from mmpose.models.utils.tta import flip_vectors
from mmpose.registry import KEYPOINT_CODECS, MODELS
from mmpose.utils.tensor_utils import to_numpy
from mmpose.utils.typing import (ConfigType, InstanceList, OptConfigType,
                                 OptSampleList)
from ..base_head import BaseHead

OptIntSeq = Optional[Sequence[int]]


@MODELS.register_module()
class AcupointDecoder(BaseHead):
    """Acu-RTMPOSE的穴位解码器
    
    第二阶段解码器，专门用于检测23个中医穴位。
    基于融合了锚点信息的增强特征进行精确的穴位定位。
    
    23个手部穴位包括：
    - 重要穴位：合谷、劳宫、太渊、神门等
    - 指端穴位：少商、商阳、中冲、关冲、少冲等
    - 掌部穴位：鱼际、少府、大陵等
    
    Args:
        in_channels (int): 输入特征图通道数（来自融合模块）
        out_channels (int): 输出关键点数量，固定为23
        input_size (tuple): 输入图像尺寸 [w, h]
        in_featuremap_size (tuple): 输入特征图尺寸 [w, h]
        simcc_split_ratio (float): SimCC分割比例，默认2.0
        final_layer_kernel_size (int): 最终卷积层核大小，默认5
        gau_cfg (dict): GAU配置
        loss (dict): 损失函数配置
        decoder (dict, optional): 解码器配置
        init_cfg (dict, optional): 初始化配置
        acupoint_weights (list, optional): 穴位权重配置
    """

    def __init__(
        self,
        in_channels: Union[int, Sequence[int]],
        out_channels: int = 23,  # 固定为23个穴位
        input_size: Tuple[int, int] = (256, 192),
        in_featuremap_size: Tuple[int, int] = (8, 6),
        simcc_split_ratio: float = 2.0,
        final_layer_kernel_size: int = 5,  # 相对较小的核，要求更高精度
        gau_cfg: ConfigType = dict(
            hidden_dims=256,
            s=128,
            expansion_factor=2,
            dropout_rate=0.,
            drop_path=0.,
            act_fn='SiLU',
            use_rel_bias=False,
            pos_enc=False),
        loss: ConfigType = dict(type='KLDiscretLoss', use_target_weight=True),
        decoder: OptConfigType = None,
        acupoint_weights: Optional[list] = None,
        init_cfg: OptConfigType = None,
    ):

        if init_cfg is None:
            init_cfg = self.default_init_cfg

        super().__init__(init_cfg)

        # 验证输出通道数
        if out_channels != 23:
            warnings.warn(f'AcupointDecoder expects 23 output channels for acupoints, '
                         f'but got {out_channels}. Setting to 23.')
            out_channels = 23

        self.in_channels = in_channels
        self.out_channels = out_channels
        self.input_size = input_size
        self.in_featuremap_size = in_featuremap_size
        self.simcc_split_ratio = simcc_split_ratio

        self.loss_module = MODELS.build(loss)
        if decoder is not None:
            self.decoder = KEYPOINT_CODECS.build(decoder)
        else:
            self.decoder = None

        if isinstance(in_channels, (tuple, list)):
            raise ValueError(
                f'{self.__class__.__name__} does not support selecting '
                'multiple input features.')

        # 设置穴位权重
        if acupoint_weights is None:
            # 默认穴位权重：重要穴位权重更高
            self.acupoint_weights = self._get_default_acupoint_weights()
        else:
            self.acupoint_weights = acupoint_weights

        # 构建网络层
        self._build_layers(final_layer_kernel_size, gau_cfg)

    def _get_default_acupoint_weights(self):
        """获取默认的穴位权重配置"""
        # 基于穴位重要性和临床应用频率设置权重
        weights = [
            # 0-4: 重要穴位（合谷、劳宫、太渊、神门、鱼际）
            2.0, 1.8, 1.9, 1.8, 1.5,
            # 5-9: 指端穴位（少商、商阳、中冲、关冲、少冲）
            1.3, 1.3, 1.3, 1.3, 1.3,
            # 10-14: 掌部穴位
            1.4, 1.2, 1.4, 1.2, 1.3,
            # 15-19: 腕部穴位
            1.6, 1.5, 1.4, 1.5, 1.4,
            # 20-22: 其他穴位
            1.2, 1.3, 1.2
        ]
        return weights

    def _build_layers(self, final_layer_kernel_size: int, gau_cfg: dict):
        """构建网络层"""
        # 计算展平维度
        flatten_dims = self.in_featuremap_size[0] * self.in_featuremap_size[1]

        # 最终卷积层 - 使用较小核卷积提高精度
        self.final_layer = nn.Conv2d(
            self.in_channels,
            self.out_channels,
            kernel_size=final_layer_kernel_size,
            stride=1,
            padding=final_layer_kernel_size // 2)

        # MLP层 - 特征变换
        self.mlp = nn.Sequential(
            ScaleNorm(flatten_dims),
            nn.Linear(flatten_dims, gau_cfg['hidden_dims'], bias=False))

        # 计算SimCC输出尺寸
        W = int(self.input_size[0] * self.simcc_split_ratio)  # 512
        H = int(self.input_size[1] * self.simcc_split_ratio)  # 384

        # GAU (Gated Attention Unit) - 专门针对穴位的自注意力
        self.gau = RTMCCBlock(
            self.out_channels,
            gau_cfg['hidden_dims'],
            gau_cfg['hidden_dims'],
            s=gau_cfg['s'],
            expansion_factor=gau_cfg['expansion_factor'],
            dropout_rate=gau_cfg['dropout_rate'],
            drop_path=gau_cfg['drop_path'],
            attn_type='self-attn',
            act_fn=gau_cfg['act_fn'],
            use_rel_bias=gau_cfg['use_rel_bias'],
            pos_enc=gau_cfg['pos_enc'])

        # 穴位特异性增强层
        self.acupoint_enhancement = nn.Sequential(
            nn.Linear(gau_cfg['hidden_dims'], gau_cfg['hidden_dims']),
            nn.LayerNorm(gau_cfg['hidden_dims']),
            nn.SiLU(),
            nn.Dropout(gau_cfg['dropout_rate'])
        )

        # 分类器 - 分别预测X和Y坐标
        self.cls_x = nn.Linear(gau_cfg['hidden_dims'], W, bias=False)
        self.cls_y = nn.Linear(gau_cfg['hidden_dims'], H, bias=False)

    def forward(self, feats: Tuple[Tensor]) -> Tuple[Tensor, Tensor]:
        """前向传播
        
        Args:
            feats (Tuple[Tensor]): 融合后的特征图，取最后一个
            
        Returns:
            Tuple[Tensor, Tensor]: (pred_x, pred_y)
                - pred_x: X坐标预测 [B, 23, W*split_ratio]
                - pred_y: Y坐标预测 [B, 23, H*split_ratio]
        """
        # 取最后一个特征图 [B, C, H, W]
        feats = feats[-1]

        # 最终卷积层 -> [B, 23, H, W]
        feats = self.final_layer(feats)

        # 展平特征图 -> [B, 23, H*W]
        feats = torch.flatten(feats, 2)

        # MLP变换 -> [B, 23, hidden_dims]
        feats = self.mlp(feats)

        # GAU自注意力 -> [B, 23, hidden_dims]
        feats = self.gau(feats)

        # 穴位特异性增强 -> [B, 23, hidden_dims]
        feats = self.acupoint_enhancement(feats)

        # 分类预测
        pred_x = self.cls_x(feats)  # [B, 23, W*split_ratio]
        pred_y = self.cls_y(feats)  # [B, 23, H*split_ratio]

        return pred_x, pred_y

    def predict(
        self,
        feats: Tuple[Tensor],
        batch_data_samples: OptSampleList,
        test_cfg: OptConfigType = {},
    ) -> InstanceList:
        """预测穴位坐标
        
        Args:
            feats: 输入特征
            batch_data_samples: 批次数据样本
            test_cfg: 测试配置
            
        Returns:
            List[InstanceData]: 预测结果，包含穴位坐标和置信度
        """
        if test_cfg.get('flip_test', False):
            # TTA: 翻转测试
            assert isinstance(feats, list) and len(feats) == 2
            flip_indices = batch_data_samples[0].metainfo.get('flip_indices', [])
            _feats, _feats_flip = feats

            _batch_pred_x, _batch_pred_y = self.forward(_feats)
            _batch_pred_x_flip, _batch_pred_y_flip = self.forward(_feats_flip)

            # 翻转处理
            _batch_pred_x_flip, _batch_pred_y_flip = flip_vectors(
                _batch_pred_x_flip,
                _batch_pred_y_flip,
                flip_indices=flip_indices)

            # 平均融合
            batch_pred_x = (_batch_pred_x + _batch_pred_x_flip) * 0.5
            batch_pred_y = (_batch_pred_y + _batch_pred_y_flip) * 0.5
        else:
            batch_pred_x, batch_pred_y = self.forward(feats)

        # 解码预测结果
        preds = self.decode((batch_pred_x, batch_pred_y))

        return preds

    def loss(
        self,
        feats: Tuple[Tensor],
        batch_data_samples: OptSampleList,
        train_cfg: OptConfigType = {},
    ) -> dict:
        """计算损失
        
        Args:
            feats: 输入特征
            batch_data_samples: 批次数据样本
            train_cfg: 训练配置
            
        Returns:
            dict: 损失字典
        """
        pred_x, pred_y = self.forward(feats)

        # 提取穴位标签
        gt_x = torch.cat([
            d.gt_instances.acupoint_simcc_x for d in batch_data_samples
        ], dim=0)
        gt_y = torch.cat([
            d.gt_instances.acupoint_simcc_y for d in batch_data_samples
        ], dim=0)
        keypoint_weights = torch.cat([
            d.gt_instances.acupoint_weights for d in batch_data_samples
        ], dim=0)

        # 计算损失 - KLDiscretLoss期望(pred_x, pred_y)作为第一个参数
        pred_simcc = (pred_x, pred_y)
        gt_simcc = (gt_x, gt_y)
        loss = self.loss_module(pred_simcc, gt_simcc, keypoint_weights)

        losses = dict()
        losses['loss_acupoint'] = loss

        # 计算准确率（用于监控）
        _, avg_acc, _ = simcc_pck_accuracy(
            output=(to_numpy(pred_x), to_numpy(pred_y)),
            target=(to_numpy(gt_x), to_numpy(gt_y)),
            simcc_split_ratio=self.simcc_split_ratio,
            mask=to_numpy(keypoint_weights) > 0)
        acc_pose = torch.tensor(avg_acc, device=gt_x.device)
        losses['acc_pose_acupoint'] = acc_pose

        return losses

    def decode(self, encoded: Tuple[Tensor, Tensor]) -> InstanceList:
        """解码SimCC预测为坐标
        
        Args:
            encoded: (pred_x, pred_y) SimCC预测
            
        Returns:
            List[InstanceData]: 解码后的穴位坐标
        """
        if self.decoder is None:
            raise RuntimeError(
                f'The decoder has not been set in {self.__class__.__name__}. '
                'Please set the decoder configs in the init parameters to '
                'enable head methods `head.predict()` and `head.decode()`')

        return self.decoder.decode(encoded)

    @property
    def default_init_cfg(self):
        init_cfg = [
            dict(type='Normal', layer=['Conv2d'], std=0.001),
            dict(type='Constant', layer='BatchNorm2d', val=1),
            dict(type='Normal', layer=['Linear'], std=0.01, bias=0),
        ]
        return init_cfg
