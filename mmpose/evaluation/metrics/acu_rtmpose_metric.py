# Copyright (c) OpenMMLab. All rights reserved.
"""
Acu-RTMPOSE评估指标
基于COCO风格的AP计算，专门为层次化穴位检测任务设计
支持分别评估锚点和穴位的AP指标，以及综合评估
"""

import datetime
import os
import os.path as osp
import tempfile
import json
from collections import OrderedDict, defaultdict
from typing import Dict, Optional, Sequence, List, Tuple

import numpy as np
from mmengine.evaluator import BaseMetric
from mmengine.fileio import dump, get_local_path, load
from mmengine.logging import MessageHub, MMLogger, print_log
from xtcocotools.coco import COCO
from xtcocotools.cocoeval import COCOeval

from mmpose.registry import METRICS
from mmpose.structures.bbox import bbox_xyxy2xywh
from ..functional import (oks_nms, soft_oks_nms, transform_ann, transform_pred,
                          transform_sigmas)


@METRICS.register_module()
class AcuRTMPoseMetric(BaseMetric):
    """Acu-RTMPOSE评估指标
    
    基于COCO风格的AP计算，专门为层次化穴位检测任务设计。
    支持分别评估锚点(21个MediaPipe关键点)和穴位(23个中医穴位)的AP指标。
    
    主要特点：
    1. 分层评估：分别计算锚点AP和穴位AP
    2. 不同sigma值：锚点和穴位使用不同的OKS计算参数
    3. 综合评分：根据权重计算综合AP分数
    4. 兼容COCO格式：使用标准的COCO评估流程
    
    Args:
        ann_file (str, optional): 标注文件路径
        use_area (bool): 是否使用面积归一化。默认True
        iou_type (str): IOU类型，默认'keypoints'
        score_mode (str): 评分模式，默认'bbox_keypoint'
        keypoint_score_thr (float): 关键点置信度阈值，默认0.2
        nms_mode (str): NMS模式，默认'oks_nms'
        nms_thr (float): NMS阈值，默认0.9
        format_only (bool): 是否只格式化结果不评估，默认False
        anchor_weight (float): 锚点权重，默认0.4
        acupoint_weight (float): 穴位权重，默认0.6
        separate_eval (bool): 是否分别评估锚点和穴位，默认True
        outfile_prefix (str, optional): 输出文件前缀
        collect_device (str): 收集设备，默认'cpu'
        prefix (str, optional): 指标前缀
    """

    def __init__(self,
                 ann_file: Optional[str] = None,
                 use_area: bool = True,
                 iou_type: str = 'keypoints',
                 score_mode: str = 'bbox_keypoint',
                 keypoint_score_thr: float = 0.2,
                 nms_mode: str = 'oks_nms',
                 nms_thr: float = 0.9,
                 format_only: bool = False,
                 anchor_weight: float = 0.4,
                 acupoint_weight: float = 0.6,
                 separate_eval: bool = True,
                 outfile_prefix: Optional[str] = None,
                 collect_device: str = 'cpu',
                 prefix: Optional[str] = None) -> None:
        super().__init__(collect_device=collect_device, prefix=prefix)
        
        self.ann_file = ann_file
        self.use_area = use_area
        self.iou_type = iou_type
        self.score_mode = score_mode
        self.keypoint_score_thr = keypoint_score_thr
        self.nms_mode = nms_mode
        self.nms_thr = nms_thr
        self.format_only = format_only
        self.anchor_weight = anchor_weight
        self.acupoint_weight = acupoint_weight
        self.separate_eval = separate_eval
        self.outfile_prefix = outfile_prefix
        
        # 初始化COCO对象
        if ann_file is not None:
            with get_local_path(ann_file) as local_path:
                self.coco = COCO(local_path)
        else:
            self.coco = None
            
        # Acu-RTMPOSE特定的sigma配置
        self.anchor_sigmas = np.array([
            # MediaPipe手部21个关键点的sigma值
            0.025,                               # WRIST
            0.035, 0.037, 0.047, 0.047,        # Thumb
            0.026, 0.025, 0.024, 0.035,        # Index finger
            0.026, 0.025, 0.024, 0.035,        # Middle finger
            0.026, 0.025, 0.024, 0.035,        # Ring finger
            0.026, 0.025, 0.024, 0.035,        # Pinky finger
        ])
        
        self.acupoint_sigmas = np.array([
            # 23个穴位的sigma值（更小，要求更高精度）
            0.020, 0.025, 0.020, 0.020, 0.018,  # Important acupoints
            0.022, 0.020, 0.025, 0.022, 0.025,
            0.028, 0.028, 0.018, 0.022, 0.025,
            0.028, 0.022, 0.025, 0.022, 0.025,
            0.028, 0.025, 0.022,
        ])
        
        # 合并的sigma值 [穴位23个 + 锚点21个]
        self.combined_sigmas = np.concatenate([self.acupoint_sigmas, self.anchor_sigmas])

    def process(self, data_batch: Sequence[dict],
                data_samples: Sequence[dict]) -> None:
        """处理一个批次的数据样本和预测结果
        
        Args:
            data_batch: 数据批次
            data_samples: 数据样本，包含预测结果和GT
        """
        for data_sample in data_samples:
            # 提取预测结果
            pred_instances = data_sample.get('pred_instances', {})
            
            # 分离锚点和穴位预测
            if 'keypoints' in pred_instances:
                keypoints = pred_instances['keypoints']  # [N, 44, 2] (23穴位+21锚点)
                keypoint_scores = pred_instances.get('keypoint_scores', None)
                
                # 分离穴位和锚点
                acupoint_coords = keypoints[:, :23, :]  # 前23个是穴位
                anchor_coords = keypoints[:, 23:, :]    # 后21个是锚点
                
                if keypoint_scores is not None:
                    acupoint_scores = keypoint_scores[:, :23]
                    anchor_scores = keypoint_scores[:, 23:]
                else:
                    acupoint_scores = None
                    anchor_scores = None
                
                # 构建结果字典
                result = {
                    'img_id': data_sample.get('img_id', 0),
                    'acupoint_coords': acupoint_coords,
                    'acupoint_scores': acupoint_scores,
                    'anchor_coords': anchor_coords,
                    'anchor_scores': anchor_scores,
                    'bboxes': pred_instances.get('bboxes', None),
                    'bbox_scores': pred_instances.get('bbox_scores', None),
                }
                
                self.results.append(result)

    def compute_metrics(self, results: list) -> Dict[str, float]:
        """计算评估指标
        
        Args:
            results: 所有批次的处理结果
            
        Returns:
            Dict[str, float]: 评估指标字典
        """
        logger: MMLogger = MMLogger.get_current_instance()
        
        # 创建临时目录
        tmp_dir = None
        if self.outfile_prefix is None:
            tmp_dir = tempfile.TemporaryDirectory()
            outfile_prefix = osp.join(tmp_dir.name, 'results')
        else:
            outfile_prefix = self.outfile_prefix
            
        try:
            eval_results = OrderedDict()
            
            if self.separate_eval:
                # 分别评估锚点和穴位
                logger.info('Evaluating Acu-RTMPOSE with separate anchor and acupoint metrics...')
                
                # 1. 评估锚点
                anchor_results = self._evaluate_keypoints(
                    results, 'anchor', self.anchor_sigmas, outfile_prefix + '_anchor')
                for key, value in anchor_results.items():
                    eval_results[f'Anchor_{key}'] = value
                    
                # 2. 评估穴位
                acupoint_results = self._evaluate_keypoints(
                    results, 'acupoint', self.acupoint_sigmas, outfile_prefix + '_acupoint')
                for key, value in acupoint_results.items():
                    eval_results[f'Acupoint_{key}'] = value
                    
                # 3. 计算综合评分
                anchor_ap = anchor_results.get('AP', 0.0)
                acupoint_ap = acupoint_results.get('AP', 0.0)
                combined_ap = (anchor_ap * self.anchor_weight + 
                              acupoint_ap * self.acupoint_weight)
                eval_results['Combined_AP'] = combined_ap
                
            else:
                # 联合评估
                logger.info('Evaluating Acu-RTMPOSE with combined metrics...')
                combined_results = self._evaluate_keypoints(
                    results, 'combined', self.combined_sigmas, outfile_prefix)
                eval_results.update(combined_results)
                
        finally:
            if tmp_dir is not None:
                tmp_dir.cleanup()
                
        return eval_results

    def _evaluate_keypoints(self, results: list, keypoint_type: str,
                           sigmas: np.ndarray, outfile_prefix: str) -> Dict[str, float]:
        """评估特定类型的关键点

        Args:
            results: 预测结果列表
            keypoint_type: 关键点类型 ('anchor', 'acupoint', 'combined')
            sigmas: 对应的sigma值
            outfile_prefix: 输出文件前缀

        Returns:
            Dict[str, float]: 评估结果
        """
        logger: MMLogger = MMLogger.get_current_instance()

        # 准备COCO格式的预测结果
        coco_results = []

        for result in results:
            img_id = result['img_id']

            if keypoint_type == 'anchor':
                coords = result['anchor_coords']
                scores = result['anchor_scores']
                num_keypoints = 21
            elif keypoint_type == 'acupoint':
                coords = result['acupoint_coords']
                scores = result['acupoint_scores']
                num_keypoints = 23
            else:  # combined
                # 合并穴位和锚点 [穴位23个 + 锚点21个]
                acupoint_coords = result['acupoint_coords']
                anchor_coords = result['anchor_coords']
                coords = np.concatenate([acupoint_coords, anchor_coords], axis=1)

                if result['acupoint_scores'] is not None and result['anchor_scores'] is not None:
                    scores = np.concatenate([result['acupoint_scores'], result['anchor_scores']], axis=1)
                else:
                    scores = None
                num_keypoints = 44

            if coords is None:
                continue

            # 处理每个检测实例
            for i in range(len(coords)):
                keypoints_flat = []

                # 转换为COCO格式 [x1, y1, v1, x2, y2, v2, ...]
                for j in range(num_keypoints):
                    x, y = coords[i, j]
                    visibility = 2 if scores is None else (2 if scores[i, j] > self.keypoint_score_thr else 0)
                    keypoints_flat.extend([float(x), float(y), visibility])

                # 计算bbox（如果没有提供）
                if result['bboxes'] is not None and i < len(result['bboxes']):
                    bbox = result['bboxes'][i]
                    area = bbox[2] * bbox[3]  # w * h
                else:
                    # 从关键点计算bbox
                    valid_coords = coords[i][scores[i] > self.keypoint_score_thr] if scores is not None else coords[i]
                    if len(valid_coords) > 0:
                        x_min, y_min = valid_coords.min(axis=0)
                        x_max, y_max = valid_coords.max(axis=0)
                        bbox = [x_min, y_min, x_max - x_min, y_max - y_min]
                        area = (x_max - x_min) * (y_max - y_min)
                    else:
                        bbox = [0, 0, 1, 1]
                        area = 1

                # 计算分数
                if self.score_mode == 'bbox_keypoint':
                    bbox_score = result['bbox_scores'][i] if result['bbox_scores'] is not None else 1.0
                    if scores is not None:
                        valid_scores = scores[i][scores[i] > self.keypoint_score_thr]
                        kpt_score = valid_scores.mean() if len(valid_scores) > 0 else 0.0
                        final_score = bbox_score * kpt_score
                    else:
                        final_score = bbox_score
                else:
                    final_score = result['bbox_scores'][i] if result['bbox_scores'] is not None else 1.0

                coco_result = {
                    'image_id': img_id,
                    'category_id': 1,  # 固定为1
                    'keypoints': keypoints_flat,
                    'score': float(final_score),
                    'bbox': [float(x) for x in bbox],
                    'area': float(area),
                }
                coco_results.append(coco_result)

        # 保存结果到JSON文件
        res_file = f'{outfile_prefix}.keypoints.json'
        dump(coco_results, res_file, sort_keys=True, indent=4)

        if self.format_only:
            logger.info(f'Results saved to {res_file}')
            return {}

        # 执行COCO评估
        if self.coco is None:
            logger.warning('No ground truth annotations available for evaluation')
            return {}

        return self._do_coco_keypoint_eval(res_file, sigmas, keypoint_type)

    def _do_coco_keypoint_eval(self, res_file: str, sigmas: np.ndarray, keypoint_type: str) -> Dict[str, float]:
        """执行COCO风格的关键点评估

        Args:
            res_file: 结果文件路径
            sigmas: sigma参数
            keypoint_type: 关键点类型，用于创建对应的GT

        Returns:
            Dict[str, float]: 评估结果
        """
        # 为不同类型的关键点创建对应的GT COCO对象
        filtered_coco = self._create_filtered_coco_gt(keypoint_type)

        coco_det = filtered_coco.loadRes(res_file)
        coco_eval = COCOeval(filtered_coco, coco_det, self.iou_type, sigmas, self.use_area)
        coco_eval.params.useSegm = None
        coco_eval.evaluate()
        coco_eval.accumulate()
        coco_eval.summarize()

        # 标准COCO关键点评估指标
        stats_names = [
            'AP', 'AP .5', 'AP .75', 'AP (M)', 'AP (L)',
            'AR', 'AR .5', 'AR .75', 'AR (M)', 'AR (L)'
        ]

        results = {}
        for name, stat in zip(stats_names, coco_eval.stats):
            results[name] = float(stat)

        return results

    def _create_filtered_coco_gt(self, keypoint_type: str):
        """为特定关键点类型创建过滤后的GT COCO对象

        Args:
            keypoint_type: 关键点类型 ('anchor', 'acupoint', 'combined')

        Returns:
            COCO: 过滤后的COCO对象
        """
        import tempfile
        from xtcocotools.coco import COCO

        # 获取原始数据
        original_data = {
            'images': self.coco.dataset['images'],
            'categories': self.coco.dataset['categories'].copy(),
            'annotations': []
        }

        # 根据关键点类型过滤和转换标注
        for ann in self.coco.dataset['annotations']:
            if 'keypoints' not in ann:
                continue

            keypoints = ann['keypoints']
            if len(keypoints) % 3 != 0:
                continue

            num_keypoints = len(keypoints) // 3

            # 重构关键点数据
            if keypoint_type == 'anchor':
                if num_keypoints == 44:
                    # 提取锚点部分 (索引23-43)
                    filtered_keypoints = []
                    for i in range(23, 44):  # 21个锚点
                        idx = i * 3
                        filtered_keypoints.extend(keypoints[idx:idx+3])
                elif num_keypoints == 21:
                    # 已经是锚点
                    filtered_keypoints = keypoints
                else:
                    continue

            elif keypoint_type == 'acupoint':
                if num_keypoints == 44:
                    # 提取穴位部分 (索引0-22)
                    filtered_keypoints = []
                    for i in range(23):  # 23个穴位
                        idx = i * 3
                        filtered_keypoints.extend(keypoints[idx:idx+3])
                elif num_keypoints == 23:
                    # 已经是穴位
                    filtered_keypoints = keypoints
                else:
                    continue

            else:  # combined
                if num_keypoints == 44:
                    filtered_keypoints = keypoints
                else:
                    # 需要扩展为44个关键点
                    if num_keypoints == 23:
                        # 穴位 + 虚拟锚点
                        filtered_keypoints = keypoints + [0, 0, 0] * 21
                    elif num_keypoints == 21:
                        # 虚拟穴位 + 锚点
                        filtered_keypoints = [0, 0, 0] * 23 + keypoints
                    else:
                        continue

            # 计算有效关键点数量
            valid_keypoints = 0
            for i in range(0, len(filtered_keypoints), 3):
                if filtered_keypoints[i+2] > 0:  # visibility > 0
                    valid_keypoints += 1

            # 创建新的标注
            new_ann = ann.copy()
            new_ann['keypoints'] = filtered_keypoints
            new_ann['num_keypoints'] = valid_keypoints
            original_data['annotations'].append(new_ann)

        # 更新类别信息中的关键点数量
        if keypoint_type == 'anchor':
            original_data['categories'][0]['keypoints'] = [f'anchor_{i}' for i in range(21)]
        elif keypoint_type == 'acupoint':
            original_data['categories'][0]['keypoints'] = [f'acupoint_{i}' for i in range(23)]
        else:  # combined
            acupoint_names = [f'acupoint_{i}' for i in range(23)]
            anchor_names = [f'anchor_{i}' for i in range(21)]
            original_data['categories'][0]['keypoints'] = acupoint_names + anchor_names

        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(original_data, f)
            temp_file = f.name

        # 创建新的COCO对象
        filtered_coco = COCO(temp_file)

        # 清理临时文件
        os.unlink(temp_file)

        return filtered_coco

    def _calculate_oks(self, pred_kpts: np.ndarray, gt_kpts: np.ndarray,
                      gt_areas: np.ndarray, sigmas: np.ndarray) -> np.ndarray:
        """计算OKS (Object Keypoint Similarity)

        Args:
            pred_kpts: 预测关键点 [N, K, 3] (x, y, visibility)
            gt_kpts: GT关键点 [M, K, 3]
            gt_areas: GT区域面积 [M]
            sigmas: sigma参数 [K]

        Returns:
            np.ndarray: OKS矩阵 [N, M]
        """
        vars = (sigmas * 2) ** 2

        # 计算距离
        pred_x = pred_kpts[:, :, 0]  # [N, K]
        pred_y = pred_kpts[:, :, 1]  # [N, K]
        pred_v = pred_kpts[:, :, 2]  # [N, K]

        gt_x = gt_kpts[:, :, 0]      # [M, K]
        gt_y = gt_kpts[:, :, 1]      # [M, K]
        gt_v = gt_kpts[:, :, 2]      # [M, K]

        # 广播计算距离
        dx = pred_x[:, None, :] - gt_x[None, :, :]  # [N, M, K]
        dy = pred_y[:, None, :] - gt_y[None, :, :]  # [N, M, K]

        # 计算归一化距离
        areas = gt_areas[None, :, None]  # [1, M, 1]
        e = (dx**2 + dy**2) / vars[None, None, :] / (areas + np.spacing(1)) / 2

        # 只考虑可见的关键点
        visibility = (pred_v[:, None, :] > 0) & (gt_v[None, :, :] > 0)  # [N, M, K]

        # 计算OKS
        oks_per_kpt = np.exp(-e) * visibility  # [N, M, K]
        oks = oks_per_kpt.sum(axis=2) / (visibility.sum(axis=2) + np.spacing(1))  # [N, M]

        return oks
