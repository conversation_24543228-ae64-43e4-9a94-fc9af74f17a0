#!/usr/bin/env python3

import json
import numpy as np

def analyze_bbox():
    """分析bbox和关键点的包含关系"""
    
    # 加载数据
    with open('/root/autodl-tmp/datasets/mix/val_with_mediapipe.json', 'r') as f:
        data = json.load(f)

    # 找到第一个穴位标注对应的图像
    for ann in data['annotations']:
        if ann['category_id'] == 1:  # 穴位标注
            img_id = ann['image_id']
            break

    print(f'分析图像ID: {img_id}')

    # 找到这个图像的所有标注
    image_annotations = [ann for ann in data['annotations'] if ann['image_id'] == img_id]

    print(f'该图像的标注数量: {len(image_annotations)}')

    # 收集所有关键点
    all_keypoints = []
    
    for i, ann in enumerate(image_annotations):
        bbox = ann['bbox']
        area = bbox[2] * bbox[3]
        category = '穴位' if ann['category_id'] == 1 else '锚点'
        
        print(f'\n{i+1}. {category} (ID:{ann["id"]}):')
        print(f'   bbox: [{bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f}]')
        print(f'   area: {area:.1f}')
        
        # 检查关键点范围
        if 'keypoints' in ann and ann['keypoints']:
            keypoints = np.array(ann['keypoints']).reshape(-1, 3)
            coords = keypoints[:, :2]
            visible = keypoints[:, 2]
            
            valid_coords = coords[visible > 0]
            if len(valid_coords) > 0:
                all_keypoints.extend(valid_coords)
                
                x_min, y_min = valid_coords.min(axis=0)
                x_max, y_max = valid_coords.max(axis=0)
                
                print(f'   关键点范围: X[{x_min:.1f}, {x_max:.1f}], Y[{y_min:.1f}, {y_max:.1f}]')
                print(f'   关键点数量: {len(valid_coords)}')
                
                # 检查bbox是否包含所有关键点
                bbox_x1, bbox_y1 = bbox[0], bbox[1]
                bbox_x2, bbox_y2 = bbox[0] + bbox[2], bbox[1] + bbox[3]
                
                x_contained = (x_min >= bbox_x1) and (x_max <= bbox_x2)
                y_contained = (y_min >= bbox_y1) and (y_max <= bbox_y2)
                
                print(f'   包含所有关键点: {x_contained and y_contained}')
                
                if not (x_contained and y_contained):
                    print(f'   ❌ 超出范围!')
                    if not x_contained:
                        print(f'      X超出: bbox[{bbox_x1:.1f}, {bbox_x2:.1f}] vs kpts[{x_min:.1f}, {x_max:.1f}]')
                    if not y_contained:
                        print(f'      Y超出: bbox[{bbox_y1:.1f}, {bbox_y2:.1f}] vs kpts[{y_min:.1f}, {y_max:.1f}]')

    # 分析所有关键点的总体范围
    if all_keypoints:
        all_keypoints = np.array(all_keypoints)
        global_x_min, global_y_min = all_keypoints.min(axis=0)
        global_x_max, global_y_max = all_keypoints.max(axis=0)
        
        print(f'\n🔍 所有关键点的总体范围:')
        print(f'   X: [{global_x_min:.1f}, {global_x_max:.1f}]')
        print(f'   Y: [{global_y_min:.1f}, {global_y_max:.1f}]')
        
        # 计算理想的bbox
        padding = 30
        ideal_x = global_x_min - padding
        ideal_y = global_y_min - padding
        ideal_w = (global_x_max - global_x_min) + 2 * padding
        ideal_h = (global_y_max - global_y_min) + 2 * padding
        
        print(f'\n💡 建议的理想bbox (padding={padding}):')
        print(f'   [{ideal_x:.1f}, {ideal_y:.1f}, {ideal_w:.1f}, {ideal_h:.1f}]')
        print(f'   面积: {ideal_w * ideal_h:.1f}')

if __name__ == "__main__":
    analyze_bbox()
