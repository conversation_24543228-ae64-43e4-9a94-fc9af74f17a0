#!/usr/bin/env python3
"""测试配置文件是否能正确加载"""

import sys
import os
sys.path.insert(0, '/root/autodl-tmp/RTMPOSE/mmpose')

try:
    from mmengine.config import Config
    
    # 测试加载配置文件
    config_path = 'configs/hand_2d_keypoint/acu_rtmpose/acu_rtmpose-m_8xb32-420e_mix-256x192.py'
    
    print(f"正在测试配置文件: {config_path}")
    
    # 加载配置
    cfg = Config.fromfile(config_path)
    
    print("✅ 配置文件加载成功!")
    print(f"模型类型: {cfg.model.type}")
    print(f"数据集类型: {cfg.dataset_type}")
    print(f"数据根目录: {cfg.data_root}")
    print(f"最大训练轮数: {cfg.train_cfg.max_epochs}")
    print(f"验证间隔: {cfg.train_cfg.val_interval}")
    
    # 检查关键配置
    if hasattr(cfg, 'visualizer'):
        print(f"可视化器类型: {cfg.visualizer.type}")
    else:
        print("使用默认可视化器配置")
        
    print("\n配置文件验证通过! 可以开始训练。")
    
except Exception as e:
    print(f"❌ 配置文件加载失败: {e}")
    print("\n请检查配置文件中的错误。")
    sys.exit(1)
