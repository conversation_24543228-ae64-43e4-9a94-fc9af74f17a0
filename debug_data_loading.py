#!/usr/bin/env python3

import sys
import os
import numpy as np
import torch

# 添加路径
sys.path.append('/root/autodl-tmp/RTMPOSE/mmpose')
sys.path.append('/root/autodl-tmp/RTMPOSE/mmpose/mmpose/datasets/datasets/hand')

# 导入数据集
from mmpose.datasets.datasets.hand.acu_rtmpose_dataset import AcuRTMPoseDataset

def test_data_loading():
    """测试数据加载"""
    
    # 创建数据集
    dataset_config = {
        'ann_file': '/root/autodl-tmp/datasets/mix/val_with_mediapipe.json',
        'data_root': '/root/autodl-tmp/datasets/mix',
        'data_prefix': {'img': 'images'},
        'test_mode': False,
        'lazy_init': False
    }

    dataset = AcuRTMPoseDataset(**dataset_config)
    print(f'数据集长度: {len(dataset)}')

    # 测试第一个数据项
    try:
        data_info = dataset.get_data_info(0)
        print(f'\n第一个数据项的keys: {list(data_info.keys())}')

        # 获取关键点数据
        acupoint_keypoints = data_info.get('acupoint_keypoints', [])
        acupoint_visible = data_info.get('acupoint_visible', [])
        anchor_keypoints = data_info.get('anchor_keypoints', [])
        anchor_visible = data_info.get('anchor_visible', [])
        bbox = data_info.get('bbox', [])

        print(f'\n数据类型和形状:')
        print(f'  acupoint_keypoints: {type(acupoint_keypoints)}, 形状: {acupoint_keypoints.shape if hasattr(acupoint_keypoints, "shape") else "N/A"}')
        print(f'  acupoint_visible: {type(acupoint_visible)}, 形状: {acupoint_visible.shape if hasattr(acupoint_visible, "shape") else "N/A"}')
        print(f'  anchor_keypoints: {type(anchor_keypoints)}, 形状: {anchor_keypoints.shape if hasattr(anchor_keypoints, "shape") else "N/A"}')
        print(f'  anchor_visible: {type(anchor_visible)}, 形状: {anchor_visible.shape if hasattr(anchor_visible, "shape") else "N/A"}')
        print(f'  bbox: {type(bbox)}, 长度: {len(bbox) if hasattr(bbox, "__len__") else "N/A"}')
        print(f'  bbox内容: {bbox}')

        # 测试条件判断
        print(f'\n条件判断测试:')
        print(f'  isinstance(acupoint_keypoints, np.ndarray): {isinstance(acupoint_keypoints, np.ndarray)}')
        if isinstance(acupoint_keypoints, np.ndarray):
            print(f'  acupoint_keypoints.size > 0: {acupoint_keypoints.size > 0}')
        
        print(f'  isinstance(anchor_keypoints, np.ndarray): {isinstance(anchor_keypoints, np.ndarray)}')
        if isinstance(anchor_keypoints, np.ndarray):
            print(f'  anchor_keypoints.size > 0: {anchor_keypoints.size > 0}')
        
        print(f'  len(bbox) == 4: {len(bbox) == 4}')
        
        # 测试组合条件
        has_acupoint = isinstance(acupoint_keypoints, np.ndarray) and acupoint_keypoints.size > 0
        has_anchor = isinstance(anchor_keypoints, np.ndarray) and anchor_keypoints.size > 0
        has_bbox = len(bbox) == 4
        
        print(f'\n组合条件:')
        print(f'  has_acupoint: {has_acupoint}')
        print(f'  has_anchor: {has_anchor}')
        print(f'  has_bbox: {has_bbox}')
        print(f'  all conditions: {has_acupoint and has_anchor and has_bbox}')

    except Exception as e:
        print(f'❌ 数据加载失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data_loading()
