#!/usr/bin/env python3
"""
Acu-RTMPOSE预测结果可视化
使用真实数据展示模型预测效果
"""

import sys
import os
import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
import json

# 添加mmpose到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def load_real_image_and_annotation():
    """加载真实图像和标注"""

    # 加载数据集
    with open('/root/autodl-tmp/datasets/mix/val_with_mediapipe.json', 'r') as f:
        data = json.load(f)

    # 找到一个有完整标注的样本
    for ann in data['annotations']:
        if ann['category_id'] == 1:  # 穴位标注
            # 找到对应的图像
            img_id = ann['image_id']
            img_info = next(img for img in data['images'] if img['id'] == img_id)

            # 检查图像文件是否存在
            img_path = f"/root/autodl-tmp/datasets/mix/images/{img_info['file_name']}"
            if os.path.exists(img_path):
                return img_path, ann, img_info, data['annotations'], img_id

    return None, None, None, None, None

def create_acu_rtmpose_model():
    """创建Acu-RTMPOSE模型"""
    
    from mmpose.models.heads.coord_cls_heads import AnchorDecoder, AcupointDecoder
    from mmpose.models.utils import AnchorFeatureFusion
    import torch.nn as nn
    
    class SimplifiedBackbone(nn.Module):
        def __init__(self):
            super().__init__()
            self.features = nn.Sequential(
                nn.Conv2d(3, 64, 3, 2, 1),
                nn.BatchNorm2d(64),
                nn.ReLU(inplace=True),
                nn.Conv2d(64, 128, 3, 2, 1),
                nn.BatchNorm2d(128),
                nn.ReLU(inplace=True),
                nn.Conv2d(128, 256, 3, 2, 1),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True),
                nn.Conv2d(256, 512, 3, 2, 1),
                nn.BatchNorm2d(512),
                nn.ReLU(inplace=True),
                nn.Conv2d(512, 768, 3, 2, 1),
                nn.BatchNorm2d(768),
                nn.ReLU(inplace=True),
            )
        
        def forward(self, x):
            return (self.features(x),)
    
    class AcuRTMPoseModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.backbone = SimplifiedBackbone()
            self.anchor_decoder = AnchorDecoder(
                in_channels=768, out_channels=21, input_size=(256, 192),
                in_featuremap_size=(8, 6), simcc_split_ratio=2.0)
            self.fusion_module = AnchorFeatureFusion(
                backbone_channels=768, anchor_channels=256, fusion_channels=768,
                num_anchors=21, fusion_type='attention')
            self.acupoint_decoder = AcupointDecoder(
                in_channels=768, out_channels=23, input_size=(256, 192),
                in_featuremap_size=(8, 6), simcc_split_ratio=2.0)
        
        def forward(self, x):
            # Backbone
            backbone_features = self.backbone(x)
            
            # 锚点解码
            anchor_pred_x, anchor_pred_y = self.anchor_decoder(backbone_features)
            anchor_coords = self.decode_coords(anchor_pred_x, anchor_pred_y)
            anchor_confidence = self.compute_confidence(anchor_pred_x, anchor_pred_y)
            
            # 信息融合
            fused_features = self.fusion_module(
                backbone_features=backbone_features[0],
                anchor_coords=anchor_coords,
                anchor_confidence=anchor_confidence)
            
            # 穴位解码
            acupoint_pred_x, acupoint_pred_y = self.acupoint_decoder((fused_features,))
            acupoint_coords = self.decode_coords(acupoint_pred_x, acupoint_pred_y)
            acupoint_confidence = self.compute_confidence(acupoint_pred_x, acupoint_pred_y)
            
            return {
                'anchor_coords': anchor_coords,
                'anchor_confidence': anchor_confidence,
                'acupoint_coords': acupoint_coords,
                'acupoint_confidence': acupoint_confidence,
            }
        
        def decode_coords(self, pred_x, pred_y, split_ratio=2.0):
            x_indices = torch.argmax(pred_x, dim=-1).float()
            y_indices = torch.argmax(pred_y, dim=-1).float()
            x_coords = x_indices / split_ratio
            y_coords = y_indices / split_ratio
            coords = torch.stack([x_coords, y_coords], dim=-1)
            return coords
        
        def compute_confidence(self, pred_x, pred_y):
            conf_x = torch.max(torch.softmax(pred_x, dim=-1), dim=-1)[0]
            conf_y = torch.max(torch.softmax(pred_y, dim=-1), dim=-1)[0]
            confidence = (conf_x + conf_y) / 2.0
            return confidence
    
    return AcuRTMPoseModel()

def preprocess_image(image_path, gt_bbox, target_size=(256, 192)):
    """预处理图像（使用GT bbox裁剪）"""

    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图像: {image_path}")

    original_image = image.copy()

    # 转换为RGB
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

    # 使用GT bbox裁剪手部区域
    bbox_x, bbox_y, bbox_w, bbox_h = gt_bbox
    x1, y1 = int(bbox_x), int(bbox_y)
    x2, y2 = int(bbox_x + bbox_w), int(bbox_y + bbox_h)

    # 确保坐标在图像范围内
    h, w = image_rgb.shape[:2]
    x1 = max(0, min(w-1, x1))
    y1 = max(0, min(h-1, y1))
    x2 = max(x1+1, min(w, x2))
    y2 = max(y1+1, min(h, y2))

    # 裁剪手部区域
    hand_region = image_rgb[y1:y2, x1:x2]
    print(f"   - 原图尺寸: {image_rgb.shape}")
    print(f"   - 裁剪区域: [{x1}, {y1}, {x2}, {y2}]")
    print(f"   - 手部区域: {hand_region.shape}")

    # 调整尺寸
    image_resized = cv2.resize(hand_region, target_size)

    # 归一化
    image_normalized = image_resized.astype(np.float32) / 255.0

    # 标准化
    mean = np.array([0.485, 0.456, 0.406])
    std = np.array([0.229, 0.224, 0.225])
    image_standardized = (image_normalized - mean) / std

    # 转换为tensor [1, 3, H, W]，确保是float32类型
    image_tensor = torch.from_numpy(image_standardized.transpose(2, 0, 1)).unsqueeze(0).float()

    return image_tensor, original_image, image_resized, hand_region

def create_optimal_bbox(annotations, image_id):
    """创建能包含所有关键点的最优bbox"""
    # 找到同一图片的所有标注
    image_annotations = [ann for ann in annotations if ann['image_id'] == image_id]

    if not image_annotations:
        raise ValueError(f"未找到图片 {image_id} 的标注")

    # 收集所有关键点
    all_keypoints = []

    for ann in image_annotations:
        bbox = ann['bbox']  # [x, y, w, h]
        area = bbox[2] * bbox[3]  # w * h
        category = '穴位' if ann['category_id'] == 1 else '锚点'

        print(f"   - {category}: bbox=[{bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f}], area={area:.1f}")

        # 收集关键点
        if 'keypoints' in ann and ann['keypoints']:
            keypoints = np.array(ann['keypoints']).reshape(-1, 3)
            coords = keypoints[:, :2]
            visible = keypoints[:, 2]

            valid_coords = coords[visible > 0]
            if len(valid_coords) > 0:
                all_keypoints.extend(valid_coords)

    if not all_keypoints:
        # 如果没有关键点，返回最大的bbox
        max_area = 0
        best_bbox = None
        for ann in image_annotations:
            bbox = ann['bbox']
            area = bbox[2] * bbox[3]
            if area > max_area:
                max_area = area
                best_bbox = bbox
        return best_bbox

    # 计算包含所有关键点的bbox
    all_keypoints = np.array(all_keypoints)
    x_min, y_min = all_keypoints.min(axis=0)
    x_max, y_max = all_keypoints.max(axis=0)

    # 添加更大的padding以确保包含所有预测点
    padding = 80  # 增加padding以包含预测误差
    optimal_x = x_min - padding
    optimal_y = y_min - padding
    optimal_w = (x_max - x_min) + 2 * padding
    optimal_h = (y_max - y_min) + 2 * padding

    optimal_bbox = [optimal_x, optimal_y, optimal_w, optimal_h]

    print(f"   - 关键点范围: X[{x_min:.1f}, {x_max:.1f}], Y[{y_min:.1f}, {y_max:.1f}]")
    print(f"   - 最优bbox: [{optimal_x:.1f}, {optimal_y:.1f}, {optimal_w:.1f}, {optimal_h:.1f}]")
    print(f"   - 最优面积: {optimal_w * optimal_h:.1f}")

    return optimal_bbox

def parse_gt_keypoints(annotation):
    """解析GT关键点"""

    keypoints_coco = annotation['keypoints']

    # COCO格式转换
    keypoints_coco = np.array(keypoints_coco)
    num_keypoints = len(keypoints_coco) // 3

    coords = keypoints_coco.reshape(num_keypoints, 3)[:, :2]  # [N, 2]
    visible = keypoints_coco.reshape(num_keypoints, 3)[:, 2]  # [N]

    return coords, visible

def transform_coords_to_resized_image(coords, original_size, resized_size, bbox):
    """将坐标从原图变换到调整后的图像"""

    # bbox: [x, y, w, h]
    bbox_x, bbox_y, bbox_w, bbox_h = bbox

    # 1. 坐标相对于bbox的归一化
    coords_normalized = coords.copy()
    coords_normalized[:, 0] = (coords[:, 0] - bbox_x) / bbox_w
    coords_normalized[:, 1] = (coords[:, 1] - bbox_y) / bbox_h

    # 2. 映射到调整后的图像尺寸
    resized_w, resized_h = resized_size
    coords_resized = coords_normalized.copy()
    coords_resized[:, 0] = coords_normalized[:, 0] * resized_w
    coords_resized[:, 1] = coords_normalized[:, 1] * resized_h

    return coords_resized

def transform_bbox_to_resized_image(bbox, original_size, resized_size):
    """将bbox从原图变换到调整后的图像（用于可视化）"""

    # 实际上，当我们使用GT bbox裁剪并调整图像时，
    # 调整后的图像就是手部区域，所以bbox应该覆盖大部分图像
    # 但考虑到可能的padding，我们给一个稍小的区域

    resized_w, resized_h = resized_size

    # 手部区域占调整后图像的大部分，留一些边距
    margin_ratio = 0.05  # 5%的边距
    margin_x = resized_w * margin_ratio
    margin_y = resized_h * margin_ratio

    return [
        margin_x,                    # x
        margin_y,                    # y
        resized_w - 2 * margin_x,    # width
        resized_h - 2 * margin_y     # height
    ]

def draw_bbox_on_axis(ax, bbox, color='green', label='BBox', linewidth=2):
    """在axis上绘制bbox"""

    # bbox: [x, y, w, h] 或 [x1, y1, x2, y2]
    if len(bbox) == 4:
        if bbox[2] > bbox[0] and bbox[3] > bbox[1]:
            # [x1, y1, x2, y2] 格式
            x1, y1, x2, y2 = bbox
            width = x2 - x1
            height = y2 - y1
        else:
            # [x, y, w, h] 格式
            x1, y1, width, height = bbox

    # 绘制矩形框
    from matplotlib.patches import Rectangle
    rect = Rectangle((x1, y1), width, height,
                    linewidth=linewidth, edgecolor=color,
                    facecolor='none', label=label)
    ax.add_patch(rect)

def visualize_predictions(image_resized, gt_coords, gt_visible, pred_coords, pred_confidence, gt_bbox, save_path):
    """可视化预测结果（包含GT bbox）"""

    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # 1. 原图 + GT
    axes[0].imshow(image_resized)
    axes[0].set_title('Ground Truth (Acupoints + GT BBox)', fontsize=14)

    # 绘制GT bbox（重要！）
    draw_bbox_on_axis(axes[0], gt_bbox, color='green', label='GT BBox', linewidth=3)

    # 绘制GT关键点（只有穴位）
    for i, (coord, vis) in enumerate(zip(gt_coords, gt_visible)):
        if vis > 0:
            color = 'red'  # GT都是穴位，用红色
            axes[0].plot(coord[0], coord[1], 'o', color=color, markersize=6)
            axes[0].text(coord[0]+2, coord[1]+2, f'A{i}', fontsize=8, color='white',
                        bbox=dict(boxstyle="round,pad=0.2", facecolor=color, alpha=0.7))
    
    # 2. 预测结果
    axes[1].imshow(image_resized)
    axes[1].set_title('Predictions (Keypoints + GT BBox)', fontsize=14)

    # 绘制GT bbox（预测中也使用GT bbox）
    draw_bbox_on_axis(axes[1], gt_bbox, color='green', label='GT BBox', linewidth=2)

    # 绘制预测关键点
    acupoint_coords = pred_coords[:23]  # 前23个是穴位
    anchor_coords = pred_coords[23:]    # 后21个是锚点
    acupoint_conf = pred_confidence[:23]
    anchor_conf = pred_confidence[23:]
    
    # 穴位（红色）
    for i, (coord, conf) in enumerate(zip(acupoint_coords, acupoint_conf)):
        alpha = min(1.0, conf * 2)  # 置信度影响透明度
        axes[1].plot(coord[0], coord[1], 'o', color='red', markersize=6, alpha=alpha)
        axes[1].text(coord[0]+2, coord[1]+2, f'A{i}', fontsize=8, color='white',
                    bbox=dict(boxstyle="round,pad=0.2", facecolor='red', alpha=0.7))
    
    # 锚点（蓝色）
    for i, (coord, conf) in enumerate(zip(anchor_coords, anchor_conf)):
        alpha = min(1.0, conf * 2)
        axes[1].plot(coord[0], coord[1], 'o', color='blue', markersize=6, alpha=alpha)
        axes[1].text(coord[0]+2, coord[1]+2, f'M{i}', fontsize=8, color='white',
                    bbox=dict(boxstyle="round,pad=0.2", facecolor='blue', alpha=0.7))
    
    # 3. 对比图（只对比穴位部分）
    axes[2].imshow(image_resized)
    axes[2].set_title('GT vs Predicted (with GT BBox)', fontsize=14)

    # 绘制GT bbox
    draw_bbox_on_axis(axes[2], gt_bbox, color='green', label='GT BBox', linewidth=2)

    # GT穴位（空心圆）
    for i, (coord, vis) in enumerate(zip(gt_coords, gt_visible)):
        if vis > 0:
            axes[2].plot(coord[0], coord[1], 'o', color='red', markersize=8,
                        fillstyle='none', linewidth=2, label='GT Acupoints' if i == 0 else "")

    # 预测穴位（实心叉）
    for i, (coord, conf) in enumerate(zip(acupoint_coords, acupoint_conf)):
        alpha = min(1.0, conf * 2)
        axes[2].plot(coord[0], coord[1], 'x', color='red', markersize=6,
                    alpha=alpha, markeredgewidth=2, label='Pred Acupoints' if i == 0 else "")

    # 预测锚点（蓝色点，仅显示不对比）
    for i, (coord, conf) in enumerate(zip(anchor_coords, anchor_conf)):
        alpha = min(1.0, conf * 2)
        axes[2].plot(coord[0], coord[1], '.', color='blue', markersize=4,
                    alpha=alpha, label='Pred Anchors' if i == 0 else "")
    
    # 设置图例和样式
    for ax in axes:
        ax.set_xlim(0, image_resized.shape[1])
        ax.set_ylim(image_resized.shape[0], 0)
        ax.axis('off')
    
    # 添加图例
    axes[2].legend(loc='upper right')
    
    # 添加统计信息
    avg_acupoint_conf = np.mean(pred_confidence[:23])
    avg_anchor_conf = np.mean(pred_confidence[23:])
    
    fig.suptitle(f'Acu-RTMPOSE Prediction Results\n'
                f'Avg Acupoint Confidence: {avg_acupoint_conf:.3f}, '
                f'Avg Anchor Confidence: {avg_anchor_conf:.3f}', 
                fontsize=16)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    print(f"可视化结果保存到: {save_path}")
    
    return fig

def main():
    """主函数"""
    print("🎨 Acu-RTMPOSE预测结果可视化")
    print("=" * 50)
    
    try:
        # 1. 加载真实数据
        print("1. 加载真实图像和标注...")
        img_path, annotation, img_info, all_annotations, img_id = load_real_image_and_annotation()

        if img_path is None:
            print("❌ 无法找到有效的图像和标注")
            return False

        print(f"   - 图像路径: {img_path}")
        print(f"   - 图像尺寸: {img_info['width']}×{img_info['height']}")
        print(f"   - 标注ID: {annotation['id']}")

        # 2. 创建包含所有关键点的最优bbox
        print("\n2. 创建最优bbox...")
        bbox = create_optimal_bbox(all_annotations, img_id)

        # 3. 解析GT关键点（使用穴位标注）
        print("\n3. 解析GT关键点...")
        gt_coords, gt_visible = parse_gt_keypoints(annotation)
        print(f"   - GT关键点数: {len(gt_coords)}")
        print(f"   - 可见关键点: {np.sum(gt_visible)}")
        print(f"   - 使用bbox: [{bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f}]")

        # 4. 预处理图像（使用最大bbox裁剪）
        print("\n4. 预处理图像...")
        image_tensor, original_image, image_resized, hand_region = preprocess_image(img_path, bbox)
        print(f"   - 原始尺寸: {original_image.shape}")
        print(f"   - 手部区域: {hand_region.shape}")
        print(f"   - 调整后尺寸: {image_resized.shape}")
        print(f"   - 模型输入: {image_tensor.shape}")

        # 5. 变换GT坐标到裁剪后的图像
        print("\n5. 坐标变换...")
        gt_coords_resized = transform_coords_to_resized_image(
            gt_coords, (img_info['width'], img_info['height']),
            (256, 192), bbox)
        
        # 6. 创建模型并预测
        print("\n6. 模型预测...")
        model = create_acu_rtmpose_model()
        model.eval()
        
        with torch.no_grad():
            outputs = model(image_tensor)
        
        # 提取预测结果
        anchor_coords = outputs['anchor_coords'][0].numpy()  # [21, 2]
        anchor_conf = outputs['anchor_confidence'][0].numpy()  # [21]
        acupoint_coords = outputs['acupoint_coords'][0].numpy()  # [23, 2]
        acupoint_conf = outputs['acupoint_confidence'][0].numpy()  # [23]
        
        # 合并预测结果 [穴位 + 锚点]
        pred_coords = np.concatenate([acupoint_coords, anchor_coords], axis=0)  # [44, 2]
        pred_confidence = np.concatenate([acupoint_conf, anchor_conf], axis=0)  # [44]
        
        print(f"   - 预测关键点数: {len(pred_coords)}")
        print(f"   - 平均穴位置信度: {np.mean(acupoint_conf):.3f}")
        print(f"   - 平均锚点置信度: {np.mean(anchor_conf):.3f}")
        
        # 7. 可视化结果
        print("\n7. 可视化结果...")
        save_path = "acu_rtmpose_prediction_visualization.png"

        # 现在图像已经是裁剪后的手部区域，bbox就是整个图像区域
        # 但我们可以显示一个稍小的框来表示"有效手部区域"
        gt_bbox_resized = transform_bbox_to_resized_image(
            bbox, (img_info['width'], img_info['height']), (256, 192))

        fig = visualize_predictions(
            image_resized, gt_coords_resized, gt_visible,
            pred_coords, pred_confidence, gt_bbox_resized, save_path)
        
        # 8. 计算简单的评估指标
        print("\n8. 简单评估...")

        # 注意：GT只有穴位（23个），预测有穴位+锚点（44个）
        # 我们只评估穴位部分
        gt_acupoint_coords = gt_coords_resized  # GT就是穴位
        pred_acupoint_coords = pred_coords[:23]  # 预测的前23个是穴位
        gt_acupoint_visible = gt_visible  # GT可见性

        # 计算有效GT穴位的预测误差
        valid_gt_indices = gt_acupoint_visible > 0
        if np.sum(valid_gt_indices) > 0:
            valid_gt_coords = gt_acupoint_coords[valid_gt_indices]
            valid_pred_coords = pred_acupoint_coords[valid_gt_indices]

            # 计算欧氏距离
            distances = np.sqrt(np.sum((valid_gt_coords - valid_pred_coords)**2, axis=1))
            mean_error = np.mean(distances)

            print(f"   - 有效穴位数: {np.sum(valid_gt_indices)}/23")
            print(f"   - 平均预测误差: {mean_error:.2f} 像素")
            print(f"   - 误差标准差: {np.std(distances):.2f} 像素")

            # PCK计算（简化版）
            pck_thresholds = [5, 10, 20]
            for thresh in pck_thresholds:
                pck = np.mean(distances < thresh)
                print(f"   - PCK@{thresh}: {pck:.3f}")

        # 额外分析：锚点预测（没有GT对比，只看置信度分布）
        print(f"\n   锚点预测分析:")
        print(f"   - 锚点数量: 21")
        print(f"   - 平均置信度: {np.mean(anchor_conf):.3f}")
        print(f"   - 置信度范围: [{np.min(anchor_conf):.3f}, {np.max(anchor_conf):.3f}]")
        print(f"   - 高置信度锚点(>0.5): {np.sum(anchor_conf > 0.5)}/21")
        
        print("\n✅ 可视化完成！")
        print(f"💡 请查看生成的图像: {save_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 可视化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
