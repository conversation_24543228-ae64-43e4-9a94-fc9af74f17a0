#!/usr/bin/env python3
"""
使用真实数据测试Acu-RTMPOSE锚点解码器
"""

import sys
import os
import torch
import numpy as np
from pathlib import Path

# 添加mmpose到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_with_real_dataset():
    """使用真实数据集测试锚点解码器"""
    print("🔍 使用真实数据测试锚点解码器...")

    try:
        # 导入必要的模块和注册transforms
        from mmpose.datasets.datasets.hand import AcuRTMPoseDataset
        from mmpose.models.heads.coord_cls_heads import AnchorDecoder
        from mmengine.structures import InstanceData

        # 确保所有transforms都已注册
        import mmpose.datasets.transforms  # 这会注册所有transforms
        from mmpose.datasets.transforms import (
            LoadImage, GetBBoxCenterScale, TopdownAffine,
            AcuRTMPoseKeypointSeparator, AcuRTMPoseGenerateTarget, AcuRTMPosePackInputs
        )
        
        # 1. 创建数据集实例（使用简化的pipeline）
        print("1. 加载真实数据集...")
        dataset_config = {
            'ann_file': '/root/autodl-tmp/datasets/mix/val_with_mediapipe.json',
            'data_root': '/root/autodl-tmp/datasets/mix/',
            'data_prefix': {'img': 'images/'},
            'data_mode': 'topdown',
            'metainfo': {'from_file': 'configs/_base_/datasets/acu_rtmpose.py'},
            'pipeline': [],  # 使用空pipeline，我们手动处理数据
            'test_mode': True,
            'lazy_init': False
        }
        
        # 检查数据文件是否存在
        if not os.path.exists(dataset_config['ann_file']):
            print(f"❌ 数据文件不存在: {dataset_config['ann_file']}")
            print("   请确保数据文件路径正确")
            return False
        
        dataset = AcuRTMPoseDataset(**dataset_config)
        print(f"   - 数据集大小: {len(dataset)}")
        
        # 2. 直接通过数据集索引获取数据
        print("\n2. 获取真实数据标注...")

        # 检查数据集的不同属性
        print(f"   - 数据集长度: {len(dataset)}")
        print(f"   - data_list长度: {len(getattr(dataset, 'data_list', []))}")
        print(f"   - data_infos长度: {len(getattr(dataset, 'data_infos', []))}")

        # 直接使用数据集的get_data_info方法或索引访问
        real_annotations = []
        sample_indices = [0, min(2, len(dataset)-1)]

        for idx in sample_indices:
            try:
                print(f"   - 尝试获取样本{idx}...")

                # 方法1: 使用get_data_info
                if hasattr(dataset, 'get_data_info'):
                    data_info = dataset.get_data_info(idx)
                    print(f"     * 通过get_data_info获取成功")
                # 方法2: 直接访问内部数据
                elif hasattr(dataset, 'coco') and dataset.coco is not None:
                    # 从COCO API获取
                    img_ids = list(dataset.coco.imgs.keys())
                    if idx < len(img_ids):
                        img_id = img_ids[idx]
                        img_info = dataset.coco.imgs[img_id]
                        ann_ids = dataset.coco.getAnnIds(imgIds=[img_id])
                        anns = dataset.coco.loadAnns(ann_ids)

                        if anns:
                            ann = anns[0]
                            data_info = {
                                'img_id': img_id,
                                'img_path': img_info['file_name'],
                                'keypoints': ann.get('keypoints', []),
                                'keypoints_visible': ann.get('keypoints_visible', []),
                                'bbox': ann.get('bbox', []),
                                'width': img_info.get('width', 0),
                                'height': img_info.get('height', 0),
                            }
                            print(f"     * 通过COCO API获取成功")
                        else:
                            print(f"     * 样本{idx}无标注，跳过")
                            continue
                    else:
                        print(f"     * 样本{idx}超出范围，跳过")
                        continue
                else:
                    print(f"     * 无法获取样本{idx}的数据信息")
                    continue

                real_annotations.append(data_info)

                # 分析获取的数据
                keypoints = data_info.get('keypoints', [])
                keypoints_visible = data_info.get('keypoints_visible', [])

                print(f"     * 关键点数据类型: {type(keypoints)}")
                if isinstance(keypoints, (list, np.ndarray)) and len(keypoints) > 0:
                    keypoints = np.array(keypoints)
                    if len(keypoints.shape) == 1:
                        # COCO格式: [x1,y1,v1,x2,y2,v2,...]
                        num_kpts = len(keypoints) // 3
                        print(f"     * COCO格式关键点数: {num_kpts}")
                        # 转换为[N,2]格式
                        kpts_2d = keypoints.reshape(-1, 3)[:, :2]
                        vis = keypoints.reshape(-1, 3)[:, 2]
                        data_info['keypoints'] = kpts_2d
                        data_info['keypoints_visible'] = vis
                    else:
                        print(f"     * 关键点形状: {keypoints.shape}")

                print(f"     * 图像路径: {data_info.get('img_path', 'N/A')}")
                print(f"     * 图像尺寸: {data_info.get('width', 'N/A')}x{data_info.get('height', 'N/A')}")

            except Exception as e:
                print(f"     * 样本{idx}获取失败: {e}")
                import traceback
                traceback.print_exc()
                continue

        if not real_annotations:
            print("❌ 无法获取任何真实标注")
            return False

        print(f"   - 成功获取{len(real_annotations)}个样本的标注")
        
        # 3. 创建锚点解码器
        print("\n3. 创建锚点解码器...")
        anchor_decoder = AnchorDecoder(
            in_channels=768,
            out_channels=21,
            input_size=(256, 192),
            in_featuremap_size=(8, 6),
            simcc_split_ratio=2.0,
            final_layer_kernel_size=7,
        )
        
        # 4. 模拟backbone输出
        print("\n4. 模拟backbone特征提取...")
        batch_size = len(real_annotations)
        # 模拟CSPNeXt-m的输出特征
        mock_features = torch.randn(batch_size, 768, 6, 8)
        feats_tuple = (mock_features,)

        # 5. 测试前向传播
        print("\n5. 测试前向传播...")
        anchor_decoder.eval()
        with torch.no_grad():
            pred_x, pred_y = anchor_decoder.forward(feats_tuple)

        print(f"   - 预测X形状: {pred_x.shape}")
        print(f"   - 预测Y形状: {pred_y.shape}")
        print(f"   - X预测范围: [{pred_x.min():.3f}, {pred_x.max():.3f}]")
        print(f"   - Y预测范围: [{pred_y.min():.3f}, {pred_y.max():.3f}]")
        
        # 6. 基于真实关键点数据准备标签
        print("\n6. 基于真实关键点数据准备标签...")
        batch_data_samples = []

        for i, annotation in enumerate(real_annotations):
            # 创建gt_instances对象
            gt_instances = InstanceData()

            # 获取真实关键点数据
            keypoints = np.array(annotation.get('keypoints', []))
            keypoints_visible = np.array(annotation.get('keypoints_visible', []))

            print(f"   - 样本{i}: 关键点形状 {keypoints.shape}")

            if keypoints.size > 0 and len(keypoints.shape) >= 2:
                # 确保关键点数据格式正确 [N, K, 2] 或 [K, 2]
                if len(keypoints.shape) == 2:
                    keypoints = keypoints[np.newaxis, ...]  # 添加batch维度
                if len(keypoints_visible.shape) == 1:
                    keypoints_visible = keypoints_visible[np.newaxis, ...]

                # 分离锚点数据（假设后21个是锚点）
                if keypoints.shape[1] >= 44:  # 确保有足够的关键点
                    anchor_keypoints = keypoints[:, 23:44, :]  # 取锚点部分
                    anchor_visible = keypoints_visible[:, 23:44]

                    # 手动创建SimCC标签（简化版本）
                    # 这里我们创建基于真实坐标的简化SimCC标签
                    anchor_simcc_x = torch.zeros(1, 21, 512)
                    anchor_simcc_y = torch.zeros(1, 21, 384)
                    anchor_weights = torch.tensor(anchor_visible, dtype=torch.float32)

                    # 为每个有效关键点在对应位置设置高斯分布
                    for k in range(21):
                        if anchor_visible[0, k] > 0:
                            x_coord = int(anchor_keypoints[0, k, 0] * 2.0)  # simcc_split_ratio=2.0
                            y_coord = int(anchor_keypoints[0, k, 1] * 2.0)

                            # 确保坐标在有效范围内
                            x_coord = max(0, min(511, x_coord))
                            y_coord = max(0, min(383, y_coord))

                            # 设置简单的one-hot标签（实际应该是高斯分布）
                            anchor_simcc_x[0, k, x_coord] = 1.0
                            anchor_simcc_y[0, k, y_coord] = 1.0

                    gt_instances.anchor_simcc_x = anchor_simcc_x
                    gt_instances.anchor_simcc_y = anchor_simcc_y
                    gt_instances.anchor_weights = anchor_weights

                    print(f"     * 锚点权重和: {anchor_weights.sum().item()}")
                else:
                    print("     * 关键点数量不足，使用模拟数据")
                    # 使用模拟数据
                    gt_instances.anchor_simcc_x = torch.softmax(torch.randn(1, 21, 512), dim=-1)
                    gt_instances.anchor_simcc_y = torch.softmax(torch.randn(1, 21, 384), dim=-1)
                    gt_instances.anchor_weights = torch.ones(1, 21)
            else:
                print("     * 无有效关键点，使用模拟数据")
                # 使用模拟数据
                gt_instances.anchor_simcc_x = torch.softmax(torch.randn(1, 21, 512), dim=-1)
                gt_instances.anchor_simcc_y = torch.softmax(torch.randn(1, 21, 384), dim=-1)
                gt_instances.anchor_weights = torch.ones(1, 21)

            # 创建数据样本对象
            data_sample = type('DataSample', (), {})()
            data_sample.gt_instances = gt_instances

            batch_data_samples.append(data_sample)
        
        # 7. 测试损失计算
        print("\n7. 测试损失计算...")
        anchor_decoder.train()
        try:
            losses = anchor_decoder.loss(feats_tuple, batch_data_samples)
            
            print("   ✅ 损失计算成功")
            for key, value in losses.items():
                if isinstance(value, torch.Tensor):
                    print(f"   - {key}: {value.item():.4f}")
                else:
                    print(f"   - {key}: {value}")
                    
        except Exception as e:
            print(f"   ❌ 损失计算失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 8. 测试预测功能
        print("\n8. 测试预测功能...")
        anchor_decoder.eval()
        try:
            # 注意：predict方法需要decoder配置
            print("   - 跳过predict测试（需要decoder配置）")
            # preds = anchor_decoder.predict(feats_tuple, batch_data_samples)
        except Exception as e:
            print(f"   - 预测测试跳过: {e}")
        
        # 9. 分析真实数据特征
        print("\n9. 分析真实数据特征...")
        for i, annotation in enumerate(real_annotations):
            print(f"   - 样本{i}:")

            # 分析关键点数据
            keypoints = np.array(annotation.get('keypoints', []))
            keypoints_visible = np.array(annotation.get('keypoints_visible', []))

            if keypoints.size > 0:
                print(f"     * 关键点形状: {keypoints.shape}")
                print(f"     * 可见性形状: {keypoints_visible.shape}")

                # 分析有效关键点
                if len(keypoints.shape) >= 2:
                    if len(keypoints.shape) == 3:
                        kpts = keypoints[0]  # 取第一个实例
                        vis = keypoints_visible[0]
                    else:
                        kpts = keypoints
                        vis = keypoints_visible

                    valid_mask = vis > 0
                    valid_kpts = kpts[valid_mask]

                    if len(valid_kpts) > 0:
                        print(f"     * 有效关键点数: {len(valid_kpts)}/{len(kpts)}")
                        print(f"     * 坐标范围: X[{valid_kpts[:, 0].min():.1f}, {valid_kpts[:, 0].max():.1f}], "
                              f"Y[{valid_kpts[:, 1].min():.1f}, {valid_kpts[:, 1].max():.1f}]")

                        # 分析锚点部分（如果有足够的关键点）
                        if len(kpts) >= 44:
                            anchor_kpts = kpts[23:44]
                            anchor_vis = vis[23:44]
                            valid_anchors = anchor_kpts[anchor_vis > 0]
                            print(f"     * 有效锚点数: {len(valid_anchors)}/21")

            # 分析其他信息
            if 'img_path' in annotation:
                print(f"     * 图像路径: {annotation['img_path']}")
            if 'bbox' in annotation:
                bbox = annotation['bbox']
                if isinstance(bbox, (list, tuple, np.ndarray)) and len(bbox) >= 4:
                    bbox = np.array(bbox).flatten()
                    print(f"     * 边界框: [{bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f}]")
                else:
                    print(f"     * 边界框: {bbox}")
        
        print("\n✅ 真实数据测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 真实数据测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_format_compatibility():
    """测试数据格式兼容性"""
    print("\n🔍 测试数据格式兼容性...")
    
    try:
        # 1. 测试数据变换pipeline
        print("1. 测试数据变换pipeline...")
        from mmpose.datasets.transforms import (
            AcuRTMPoseKeypointSeparator,
            AcuRTMPoseGenerateTarget,
            AcuRTMPosePackInputs
        )
        
        # 创建模拟的原始数据（模拟从COCO格式加载的数据）
        raw_data = {
            'img': np.random.randint(0, 255, (192, 256, 3), dtype=np.uint8),
            'keypoints': np.random.rand(1, 44, 2) * 192,  # 44个关键点
            'keypoints_visible': np.ones((1, 44)),
            'bbox': np.array([[50, 50, 150, 150]]),
            'bbox_center': np.array([125, 125]),
            'bbox_scale': np.array([100, 100]),
        }
        
        print(f"   - 原始关键点形状: {raw_data['keypoints'].shape}")
        
        # 2. 测试关键点分离
        separator = AcuRTMPoseKeypointSeparator()
        separated_data = separator.transform(raw_data.copy())
        
        print(f"   - 穴位关键点形状: {separated_data['acupoint_keypoints'].shape}")
        print(f"   - 锚点关键点形状: {separated_data['anchor_keypoints'].shape}")
        
        # 3. 测试目标生成
        target_generator = AcuRTMPoseGenerateTarget(
            input_size=(256, 192),
            simcc_split_ratio=2.0,
            sigma=(4.9, 5.66)
        )
        target_data = target_generator.transform(separated_data.copy())
        
        print(f"   - 穴位SimCC X形状: {target_data['acupoint_simcc_x'].shape}")
        print(f"   - 穴位SimCC Y形状: {target_data['acupoint_simcc_y'].shape}")
        print(f"   - 锚点SimCC X形状: {target_data['anchor_simcc_x'].shape}")
        print(f"   - 锚点SimCC Y形状: {target_data['anchor_simcc_y'].shape}")
        
        # 4. 测试数据打包
        packer = AcuRTMPosePackInputs()
        packed_data = packer.transform(target_data.copy())
        
        print(f"   - 打包后输入形状: {packed_data['inputs'].shape}")
        print(f"   - 数据样本键: {list(packed_data['data_samples'].keys())}")
        
        # 5. 验证锚点数据格式
        gt_instances = packed_data['data_samples']['gt_instances']
        anchor_keys = ['anchor_simcc_x', 'anchor_simcc_y', 'anchor_weights']
        
        print("   - 锚点标签验证:")
        for key in anchor_keys:
            if key in gt_instances:
                data = gt_instances[key]
                print(f"     * {key}: {data.shape if hasattr(data, 'shape') else type(data)}")
            else:
                print(f"     * {key}: 缺失")
        
        print("✅ 数据格式兼容性测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 数据格式兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 使用真实数据测试Acu-RTMPOSE锚点解码器")
    print("=" * 60)
    
    # 测试列表
    tests = [
        ("数据格式兼容性测试", test_data_format_compatibility),
        ("真实数据集测试", test_with_real_dataset),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = sum(results)
    total = len(results)
    
    for i, ((test_name, _), result) in enumerate(zip(tests, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有真实数据测试通过！锚点解码器可以正确处理真实数据！")
        return True
    else:
        print("⚠️  部分测试失败，请检查数据格式或模型实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
