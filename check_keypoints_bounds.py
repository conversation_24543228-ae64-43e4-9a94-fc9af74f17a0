#!/usr/bin/env python3

import json
import numpy as np

def check_keypoints_bounds():
    """检查关键点是否超出bbox边界"""
    
    # 加载数据
    with open('/root/autodl-tmp/datasets/mix/val_with_mediapipe.json', 'r') as f:
        data = json.load(f)

    # 找到图像985的标注
    img_id = 985
    image_annotations = [ann for ann in data['annotations'] if ann['image_id'] == img_id]

    print(f'图像{img_id}的标注:')
    
    all_keypoints = []
    
    for ann in image_annotations:
        category = '穴位' if ann['category_id'] == 1 else '锚点'
        bbox = ann['bbox']
        print(f'\n{category}: bbox={bbox}')
        
        if 'keypoints' in ann and ann['keypoints']:
            keypoints = np.array(ann['keypoints']).reshape(-1, 3)
            coords = keypoints[:, :2]
            visible = keypoints[:, 2]
            
            valid_coords = coords[visible > 0]
            if len(valid_coords) > 0:
                all_keypoints.extend(valid_coords)
                
                x_min, y_min = valid_coords.min(axis=0)
                x_max, y_max = valid_coords.max(axis=0)
                print(f'  关键点范围: X[{x_min:.1f}, {x_max:.1f}], Y[{y_min:.1f}, {y_max:.1f}]')
                print(f'  关键点数量: {len(valid_coords)}')
                
                # 检查是否有关键点超出bbox
                bbox_x1, bbox_y1 = bbox[0], bbox[1]
                bbox_x2, bbox_y2 = bbox[0] + bbox[2], bbox[1] + bbox[3]
                
                print(f'  bbox范围: X[{bbox_x1:.1f}, {bbox_x2:.1f}], Y[{bbox_y1:.1f}, {bbox_y2:.1f}]')
                
                out_of_bounds = []
                for i, (x, y) in enumerate(valid_coords):
                    if x < bbox_x1 or x > bbox_x2 or y < bbox_y1 or y > bbox_y2:
                        out_of_bounds.append((i, x, y))
                
                if out_of_bounds:
                    print(f'  ❌ 超出bbox的关键点: {len(out_of_bounds)}个')
                    for i, x, y in out_of_bounds:
                        print(f'    点{i}: ({x:.1f}, {y:.1f})')
                else:
                    print(f'  ✅ 所有关键点都在bbox内')

    # 计算所有关键点的总体范围
    if all_keypoints:
        all_keypoints = np.array(all_keypoints)
        global_x_min, global_y_min = all_keypoints.min(axis=0)
        global_x_max, global_y_max = all_keypoints.max(axis=0)
        
        print(f'\n🔍 所有关键点的总体范围:')
        print(f'   X: [{global_x_min:.1f}, {global_x_max:.1f}]')
        print(f'   Y: [{global_y_min:.1f}, {global_y_max:.1f}]')
        
        # 计算理想的bbox（确保包含所有关键点）
        padding = 50  # 增加padding
        ideal_x = global_x_min - padding
        ideal_y = global_y_min - padding
        ideal_w = (global_x_max - global_x_min) + 2 * padding
        ideal_h = (global_y_max - global_y_min) + 2 * padding
        
        print(f'\n💡 建议的理想bbox (padding={padding}):')
        print(f'   [{ideal_x:.1f}, {ideal_y:.1f}, {ideal_w:.1f}, {ideal_h:.1f}]')
        print(f'   覆盖范围: X[{ideal_x:.1f}, {ideal_x + ideal_w:.1f}], Y[{ideal_y:.1f}, {ideal_y + ideal_h:.1f}]')
        print(f'   面积: {ideal_w * ideal_h:.1f}')

if __name__ == "__main__":
    check_keypoints_bounds()
