#!/usr/bin/env python3
"""
测试Acu-RTMPOSE的AP评估指标
演示如何使用COCO风格的AP计算来评估层次化穴位检测模型
"""

import sys
import os
import torch
import numpy as np
import json
from pathlib import Path

# 添加mmpose到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from mmpose.evaluation.metrics import AcuRTMPoseMetric
from mmengine.structures import InstanceData


def create_mock_predictions():
    """创建模拟的预测结果用于测试"""
    
    # 模拟预测结果
    predictions = []
    
    for img_id in range(5):  # 5张图片
        # 每张图片2个检测实例
        for instance_id in range(2):
            # 23个穴位坐标 + 21个锚点坐标
            acupoint_coords = np.random.rand(23, 2) * 200 + 50  # [23, 2]
            anchor_coords = np.random.rand(21, 2) * 200 + 50    # [21, 2]
            
            # 合并坐标 [44, 2]
            keypoints = np.concatenate([acupoint_coords, anchor_coords], axis=0)
            
            # 模拟置信度分数
            acupoint_scores = np.random.rand(23) * 0.5 + 0.5  # [0.5, 1.0]
            anchor_scores = np.random.rand(21) * 0.5 + 0.5    # [0.5, 1.0]
            keypoint_scores = np.concatenate([acupoint_scores, anchor_scores], axis=0)
            
            # 模拟bbox
            x_min, y_min = keypoints.min(axis=0) - 10
            x_max, y_max = keypoints.max(axis=0) + 10
            bbox = np.array([x_min, y_min, x_max - x_min, y_max - y_min])
            bbox_score = np.random.rand() * 0.3 + 0.7  # [0.7, 1.0]
            
            prediction = {
                'img_id': img_id,
                'acupoint_coords': acupoint_coords[None, :, :],  # [1, 23, 2]
                'acupoint_scores': acupoint_scores[None, :],     # [1, 23]
                'anchor_coords': anchor_coords[None, :, :],      # [1, 21, 2]
                'anchor_scores': anchor_scores[None, :],         # [1, 21]
                'bboxes': bbox[None, :],                         # [1, 4]
                'bbox_scores': np.array([bbox_score]),           # [1]
            }
            predictions.append(prediction)
    
    return predictions


def create_mock_gt_annotations():
    """创建模拟的GT标注用于测试"""
    
    # 创建COCO格式的标注文件
    coco_data = {
        "images": [],
        "annotations": [],
        "categories": [
            {
                "id": 1,
                "name": "hand",
                "supercategory": "person",
                "keypoints": [],  # 将在下面填充
                "skeleton": []
            }
        ]
    }
    
    # 添加关键点名称
    keypoint_names = []
    # 23个穴位
    acupoint_names = [
        'hegu', 'laogong', 'taiyuan', 'shenmen', 'yuji', 'shaofu', 'daling',
        'yangxi', 'yangchi', 'yanggu', 'wangu', 'shaoshang', 'shangyang',
        'zhongchong', 'guanchong', 'shaochong', 'shixuan_thumb', 'shixuan_index',
        'shixuan_middle', 'shixuan_ring', 'shixuan_pinky', 'baxie_1', 'baxie_2'
    ]
    # 21个锚点
    anchor_names = [
        'wrist', 'thumb_cmc', 'thumb_mcp', 'thumb_ip', 'thumb_tip',
        'index_mcp', 'index_pip', 'index_dip', 'index_tip',
        'middle_mcp', 'middle_pip', 'middle_dip', 'middle_tip',
        'ring_mcp', 'ring_pip', 'ring_dip', 'ring_tip',
        'pinky_mcp', 'pinky_pip', 'pinky_dip', 'pinky_tip'
    ]
    keypoint_names = acupoint_names + anchor_names
    coco_data["categories"][0]["keypoints"] = keypoint_names
    
    ann_id = 1
    for img_id in range(5):
        # 添加图像信息
        coco_data["images"].append({
            "id": img_id,
            "file_name": f"test_image_{img_id}.jpg",
            "width": 300,
            "height": 300
        })
        
        # 每张图片2个GT实例
        for instance_id in range(2):
            # 生成GT关键点
            gt_keypoints = []
            for kpt_id in range(44):  # 44个关键点
                x = np.random.rand() * 200 + 50
                y = np.random.rand() * 200 + 50
                visibility = 2  # 可见
                gt_keypoints.extend([x, y, visibility])
            
            # 计算bbox
            coords = np.array(gt_keypoints).reshape(-1, 3)[:, :2]
            x_min, y_min = coords.min(axis=0) - 5
            x_max, y_max = coords.max(axis=0) + 5
            bbox = [x_min, y_min, x_max - x_min, y_max - y_min]
            area = bbox[2] * bbox[3]
            
            coco_data["annotations"].append({
                "id": ann_id,
                "image_id": img_id,
                "category_id": 1,
                "keypoints": gt_keypoints,
                "bbox": bbox,
                "area": area,
                "iscrowd": 0,
                "num_keypoints": 44
            })
            ann_id += 1
    
    return coco_data


def test_acu_rtmpose_metric():
    """测试AcuRTMPoseMetric"""
    
    print("🧪 测试Acu-RTMPOSE AP评估指标")
    print("=" * 60)
    
    # 1. 创建临时GT标注文件
    print("1. 创建模拟GT标注...")
    gt_data = create_mock_gt_annotations()
    gt_file = "temp_gt_annotations.json"
    with open(gt_file, 'w') as f:
        json.dump(gt_data, f, indent=2)
    print(f"   - GT标注文件: {gt_file}")
    print(f"   - 图像数量: {len(gt_data['images'])}")
    print(f"   - 标注数量: {len(gt_data['annotations'])}")
    
    # 2. 创建评估器
    print("\n2. 初始化AcuRTMPoseMetric...")
    metric = AcuRTMPoseMetric(
        ann_file=gt_file,
        use_area=True,
        iou_type='keypoints',
        score_mode='bbox_keypoint',
        keypoint_score_thr=0.2,
        nms_mode='oks_nms',
        nms_thr=0.9,
        format_only=False,
        anchor_weight=0.4,
        acupoint_weight=0.6,
        separate_eval=True,
        outfile_prefix='test_acu_rtmpose_results'
    )
    print("   - 评估器初始化完成")
    print(f"   - 锚点权重: {metric.anchor_weight}")
    print(f"   - 穴位权重: {metric.acupoint_weight}")
    print(f"   - 分别评估: {metric.separate_eval}")
    
    # 3. 创建模拟预测结果
    print("\n3. 生成模拟预测结果...")
    predictions = create_mock_predictions()
    print(f"   - 预测结果数量: {len(predictions)}")
    
    # 4. 处理预测结果
    print("\n4. 处理预测结果...")
    data_batch = [{'inputs': None}] * len(predictions)
    data_samples = []
    
    for pred in predictions:
        # 构建数据样本格式
        pred_instances = {
            'keypoints': np.concatenate([
                pred['acupoint_coords'], 
                pred['anchor_coords']
            ], axis=1),  # [1, 44, 2]
            'keypoint_scores': np.concatenate([
                pred['acupoint_scores'],
                pred['anchor_scores']
            ], axis=1),  # [1, 44]
            'bboxes': pred['bboxes'],
            'bbox_scores': pred['bbox_scores']
        }
        
        data_sample = {
            'img_id': pred['img_id'],
            'pred_instances': pred_instances
        }
        data_samples.append(data_sample)
    
    # 处理批次
    for i in range(0, len(data_samples), 2):  # 每批2个样本
        batch_samples = data_samples[i:i+2]
        batch_data = data_batch[i:i+2]
        metric.process(batch_data, batch_samples)
    
    print(f"   - 处理了 {len(data_samples)} 个样本")
    
    # 5. 计算评估指标
    print("\n5. 计算AP评估指标...")
    try:
        results = metric.compute_metrics(metric.results)
        
        print("\n📊 评估结果:")
        print("-" * 40)
        
        # 显示锚点指标
        print("🔵 锚点 (Anchor) 指标:")
        for key, value in results.items():
            if key.startswith('Anchor_'):
                metric_name = key.replace('Anchor_', '')
                print(f"   {metric_name:12}: {value:.4f}")
        
        print("\n🔴 穴位 (Acupoint) 指标:")
        for key, value in results.items():
            if key.startswith('Acupoint_'):
                metric_name = key.replace('Acupoint_', '')
                print(f"   {metric_name:12}: {value:.4f}")
        
        print("\n🟢 综合指标:")
        for key, value in results.items():
            if key.startswith('Combined_'):
                metric_name = key.replace('Combined_', '')
                print(f"   {metric_name:12}: {value:.4f}")
        
        # 分析结果
        print("\n📈 结果分析:")
        anchor_ap = results.get('Anchor_AP', 0)
        acupoint_ap = results.get('Acupoint_AP', 0)
        combined_ap = results.get('Combined_AP', 0)
        
        print(f"   - 锚点AP: {anchor_ap:.4f}")
        print(f"   - 穴位AP: {acupoint_ap:.4f}")
        print(f"   - 综合AP: {combined_ap:.4f}")
        print(f"   - 权重计算验证: {anchor_ap * 0.4 + acupoint_ap * 0.6:.4f}")
        
    except Exception as e:
        print(f"❌ 评估过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    # 6. 清理临时文件
    print("\n6. 清理临时文件...")
    try:
        os.remove(gt_file)
        # 清理可能生成的结果文件
        for suffix in ['.keypoints.json', '_anchor.keypoints.json', '_acupoint.keypoints.json']:
            result_file = f'test_acu_rtmpose_results{suffix}'
            if os.path.exists(result_file):
                os.remove(result_file)
        print("   - 临时文件清理完成")
    except Exception as e:
        print(f"   - 清理文件时出现警告: {e}")
    
    print("\n✅ 测试完成！")
    return True


def main():
    """主函数"""
    print("🎯 Acu-RTMPOSE AP评估指标测试")
    print("=" * 60)
    
    try:
        success = test_acu_rtmpose_metric()
        if success:
            print("\n🎉 所有测试通过！")
            print("\n💡 使用说明:")
            print("   1. 在配置文件中设置 val_evaluator 为 AcuRTMPoseMetric")
            print("   2. 设置 separate_eval=True 可分别评估锚点和穴位")
            print("   3. 调整 anchor_weight 和 acupoint_weight 控制综合评分权重")
            print("   4. 支持标准COCO AP指标: AP, AP.5, AP.75, AP(M), AP(L), AR等")
        else:
            print("\n❌ 测试失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
