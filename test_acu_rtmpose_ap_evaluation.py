#!/usr/bin/env python3
"""
测试Acu-RTMPOSE的AP评估指标
使用真实数据来测试COCO风格的AP计算
"""

import sys
import os
import torch
import numpy as np
import json
import cv2
from pathlib import Path

# 添加mmpose到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from mmpose.evaluation.metrics import AcuRTMPoseMetric
from mmengine.structures import InstanceData


def load_real_data():
    """加载真实的数据集"""

    # 加载真实的标注文件
    ann_file = '/root/autodl-tmp/datasets/mix/val_with_mediapipe.json'

    if not os.path.exists(ann_file):
        raise FileNotFoundError(f"标注文件不存在: {ann_file}")

    with open(ann_file, 'r') as f:
        data = json.load(f)

    print(f"   - 加载标注文件: {ann_file}")
    print(f"   - 图像数量: {len(data['images'])}")
    print(f"   - 标注数量: {len(data['annotations'])}")
    print(f"   - 类别数量: {len(data['categories'])}")

    return data


def create_real_predictions_with_model(data, num_samples=10):
    """使用真实模型生成预测结果"""

    from visualize_acu_rtmpose_predictions import (
        create_acu_rtmpose_model,
        preprocess_image,
        create_optimal_bbox,
        parse_gt_keypoints,
        transform_coords_to_resized_image
    )

    print("   - 创建Acu-RTMPOSE模型...")
    model = create_acu_rtmpose_model()
    model.eval()

    predictions = []
    processed_count = 0

    # 按图像ID分组标注
    annotations_by_image = {}
    for ann in data['annotations']:
        img_id = ann['image_id']
        if img_id not in annotations_by_image:
            annotations_by_image[img_id] = []
        annotations_by_image[img_id].append(ann)

    # 处理前num_samples张图像
    for img_info in data['images'][:num_samples]:
        img_id = img_info['id']
        img_path = f"/root/autodl-tmp/datasets/mix/images/{img_info['file_name']}"

        if not os.path.exists(img_path):
            print(f"   - 跳过不存在的图像: {img_path}")
            continue

        if img_id not in annotations_by_image:
            print(f"   - 跳过无标注的图像: {img_id}")
            continue

        try:
            # 获取该图像的所有标注
            img_annotations = annotations_by_image[img_id]

            # 创建最优bbox
            bbox = create_optimal_bbox(img_annotations, img_id)

            # 预处理图像
            image_tensor, _, _, _ = preprocess_image(img_path, bbox)

            # 模型预测
            with torch.no_grad():
                outputs = model(image_tensor)

            # 提取预测结果
            anchor_coords = outputs['anchor_coords'][0].numpy()      # [21, 2]
            anchor_conf = outputs['anchor_confidence'][0].numpy()    # [21]
            acupoint_coords = outputs['acupoint_coords'][0].numpy()  # [23, 2]
            acupoint_conf = outputs['acupoint_confidence'][0].numpy() # [23]

            # 将坐标从模型输出空间转换回原图空间
            # 这里简化处理，实际应该做完整的坐标变换
            scale_x = img_info['width'] / 256
            scale_y = img_info['height'] / 192

            acupoint_coords_scaled = acupoint_coords * [scale_x, scale_y]
            anchor_coords_scaled = anchor_coords * [scale_x, scale_y]

            # 添加bbox偏移
            acupoint_coords_scaled += [bbox[0], bbox[1]]
            anchor_coords_scaled += [bbox[0], bbox[1]]

            # 计算预测bbox
            all_coords = np.concatenate([acupoint_coords_scaled, anchor_coords_scaled], axis=0)
            x_min, y_min = all_coords.min(axis=0) - 10
            x_max, y_max = all_coords.max(axis=0) + 10
            pred_bbox = np.array([x_min, y_min, x_max - x_min, y_max - y_min])

            # 计算综合置信度作为bbox分数
            bbox_score = (acupoint_conf.mean() + anchor_conf.mean()) / 2

            prediction = {
                'img_id': img_id,
                'acupoint_coords': acupoint_coords_scaled[None, :, :],  # [1, 23, 2]
                'acupoint_scores': acupoint_conf[None, :],              # [1, 23]
                'anchor_coords': anchor_coords_scaled[None, :, :],      # [1, 21, 2]
                'anchor_scores': anchor_conf[None, :],                  # [1, 21]
                'bboxes': pred_bbox[None, :],                           # [1, 4]
                'bbox_scores': np.array([bbox_score]),                  # [1]
            }
            predictions.append(prediction)
            processed_count += 1

            print(f"   - 处理图像 {img_id}: {img_info['file_name']}")
            print(f"     穴位平均置信度: {acupoint_conf.mean():.3f}")
            print(f"     锚点平均置信度: {anchor_conf.mean():.3f}")

        except Exception as e:
            print(f"   - 处理图像 {img_id} 时出错: {e}")
            continue

    print(f"   - 成功处理 {processed_count} 张图像")
    return predictions


def prepare_gt_annotations_for_evaluation(data):
    """准备用于评估的GT标注"""

    # 检查并转换标注格式
    converted_annotations = []

    for ann in data['annotations']:
        # 检查是否有keypoints字段
        if 'keypoints' not in ann:
            print(f"   - 跳过无关键点的标注: {ann['id']}")
            continue

        keypoints = ann['keypoints']

        # 检查关键点数量
        if len(keypoints) % 3 != 0:
            print(f"   - 跳过格式错误的关键点: {ann['id']}")
            continue

        num_keypoints = len(keypoints) // 3

        # 如果是穴位标注(23个点)，需要扩展为44个点(23穴位+21锚点)
        if num_keypoints == 23:
            # 添加21个虚拟锚点(不可见)
            extended_keypoints = keypoints.copy()
            for _ in range(21):
                extended_keypoints.extend([0, 0, 0])  # 不可见的锚点

            # 创建新的标注
            new_ann = ann.copy()
            new_ann['keypoints'] = extended_keypoints
            new_ann['num_keypoints'] = 23  # 只计算可见的穴位
            converted_annotations.append(new_ann)

        elif num_keypoints == 21:
            # 如果是锚点标注，添加23个虚拟穴位
            extended_keypoints = []
            # 先添加23个虚拟穴位(不可见)
            for _ in range(23):
                extended_keypoints.extend([0, 0, 0])
            # 再添加21个真实锚点
            extended_keypoints.extend(keypoints)

            # 创建新的标注
            new_ann = ann.copy()
            new_ann['keypoints'] = extended_keypoints
            new_ann['num_keypoints'] = 21  # 只计算可见的锚点
            converted_annotations.append(new_ann)

        elif num_keypoints == 44:
            # 已经是完整格式
            converted_annotations.append(ann)
        else:
            print(f"   - 跳过未知格式的标注: {ann['id']}, 关键点数: {num_keypoints}")

    # 更新数据
    new_data = data.copy()
    new_data['annotations'] = converted_annotations

    print(f"   - 转换后的标注数量: {len(converted_annotations)}")
    return new_data


def test_acu_rtmpose_metric():
    """测试AcuRTMPoseMetric"""

    print("🧪 测试Acu-RTMPOSE AP评估指标 (使用真实数据)")
    print("=" * 60)

    # 1. 加载真实数据
    print("1. 加载真实数据集...")
    try:
        gt_data = load_real_data()
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return False

    # 2. 准备GT标注
    print("\n2. 准备GT标注...")
    gt_data = prepare_gt_annotations_for_evaluation(gt_data)

    # 保存处理后的GT标注
    gt_file = "temp_real_gt_annotations.json"
    with open(gt_file, 'w') as f:
        json.dump(gt_data, f, indent=2)
    print(f"   - 处理后的GT标注文件: {gt_file}")

    # 3. 创建评估器
    print("\n3. 初始化AcuRTMPoseMetric...")
    metric = AcuRTMPoseMetric(
        ann_file=gt_file,
        use_area=True,
        iou_type='keypoints',
        score_mode='bbox_keypoint',
        keypoint_score_thr=0.2,
        nms_mode='oks_nms',
        nms_thr=0.9,
        format_only=False,
        anchor_weight=0.4,
        acupoint_weight=0.6,
        separate_eval=True,
        outfile_prefix='test_acu_rtmpose_real_results'
    )
    print("   - 评估器初始化完成")
    print(f"   - 锚点权重: {metric.anchor_weight}")
    print(f"   - 穴位权重: {metric.acupoint_weight}")
    print(f"   - 分别评估: {metric.separate_eval}")

    # 4. 使用真实模型生成预测结果
    print("\n4. 使用真实模型生成预测结果...")
    try:
        predictions = create_real_predictions_with_model(gt_data, num_samples=5)
        print(f"   - 预测结果数量: {len(predictions)}")
        if len(predictions) == 0:
            print("❌ 没有生成任何预测结果")
            return False
    except Exception as e:
        print(f"❌ 生成预测结果失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    # 5. 处理预测结果
    print("\n5. 处理预测结果...")
    data_batch = [{'inputs': None}] * len(predictions)
    data_samples = []

    for pred in predictions:
        # 构建数据样本格式
        pred_instances = {
            'keypoints': np.concatenate([
                pred['acupoint_coords'],
                pred['anchor_coords']
            ], axis=1),  # [1, 44, 2]
            'keypoint_scores': np.concatenate([
                pred['acupoint_scores'],
                pred['anchor_scores']
            ], axis=1),  # [1, 44]
            'bboxes': pred['bboxes'],
            'bbox_scores': pred['bbox_scores']
        }

        data_sample = {
            'img_id': pred['img_id'],
            'pred_instances': pred_instances
        }
        data_samples.append(data_sample)

    # 处理批次
    for i in range(0, len(data_samples), 2):  # 每批2个样本
        batch_samples = data_samples[i:i+2]
        batch_data = data_batch[i:i+2]
        metric.process(batch_data, batch_samples)

    print(f"   - 处理了 {len(data_samples)} 个样本")

    # 6. 计算评估指标
    print("\n6. 计算AP评估指标...")
    try:
        results = metric.compute_metrics(metric.results)

        print("\n📊 真实数据评估结果:")
        print("-" * 50)

        # 显示锚点指标
        print("🔵 锚点 (Anchor) 指标:")
        anchor_metrics = {}
        for key, value in results.items():
            if key.startswith('Anchor_'):
                metric_name = key.replace('Anchor_', '')
                anchor_metrics[metric_name] = value
                print(f"   {metric_name:12}: {value:.4f}")

        print("\n🔴 穴位 (Acupoint) 指标:")
        acupoint_metrics = {}
        for key, value in results.items():
            if key.startswith('Acupoint_'):
                metric_name = key.replace('Acupoint_', '')
                acupoint_metrics[metric_name] = value
                print(f"   {metric_name:12}: {value:.4f}")

        print("\n🟢 综合指标:")
        combined_metrics = {}
        for key, value in results.items():
            if key.startswith('Combined_'):
                metric_name = key.replace('Combined_', '')
                combined_metrics[metric_name] = value
                print(f"   {metric_name:12}: {value:.4f}")

        # 详细分析结果
        print("\n📈 详细结果分析:")
        anchor_ap = results.get('Anchor_AP', 0)
        acupoint_ap = results.get('Acupoint_AP', 0)
        combined_ap = results.get('Combined_AP', 0)

        print(f"   - 锚点AP: {anchor_ap:.4f}")
        print(f"   - 穴位AP: {acupoint_ap:.4f}")
        print(f"   - 综合AP: {combined_ap:.4f}")
        print(f"   - 权重计算验证: {anchor_ap * 0.4 + acupoint_ap * 0.6:.4f}")

        # 性能对比分析
        print("\n🎯 性能分析:")
        if anchor_ap > acupoint_ap:
            print(f"   - 锚点检测性能更好 (高出 {anchor_ap - acupoint_ap:.4f})")
        else:
            print(f"   - 穴位检测性能更好 (高出 {acupoint_ap - anchor_ap:.4f})")

        # 不同阈值下的性能
        print("\n📏 不同阈值下的AP:")
        for threshold in ['.5', '.75']:
            anchor_key = f'Anchor_AP {threshold}'
            acupoint_key = f'Acupoint_AP {threshold}'
            if anchor_key in results and acupoint_key in results:
                print(f"   - AP{threshold}: 锚点={results[anchor_key]:.4f}, 穴位={results[acupoint_key]:.4f}")

        # 不同尺度下的性能
        print("\n📐 不同尺度下的AP:")
        for scale in ['(M)', '(L)']:
            anchor_key = f'Anchor_AP {scale}'
            acupoint_key = f'Acupoint_AP {scale}'
            if anchor_key in results and acupoint_key in results:
                print(f"   - AP{scale}: 锚点={results[anchor_key]:.4f}, 穴位={results[acupoint_key]:.4f}")

        return True

    except Exception as e:
        print(f"❌ 评估过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        # 7. 清理临时文件
        print("\n7. 清理临时文件...")
        try:
            if os.path.exists(gt_file):
                os.remove(gt_file)
            # 清理可能生成的结果文件
            for suffix in ['.keypoints.json', '_anchor.keypoints.json', '_acupoint.keypoints.json']:
                result_file = f'test_acu_rtmpose_real_results{suffix}'
                if os.path.exists(result_file):
                    os.remove(result_file)
            print("   - 临时文件清理完成")
        except Exception as e:
            print(f"   - 清理文件时出现警告: {e}")


def main():
    """主函数"""
    print("🎯 Acu-RTMPOSE AP评估指标测试 (真实数据)")
    print("=" * 60)

    try:
        success = test_acu_rtmpose_metric()
        if success:
            print("\n🎉 真实数据测试通过！")
            print("\n💡 AP评估指标使用说明:")
            print("   1. 在配置文件中设置 val_evaluator 为 AcuRTMPoseMetric")
            print("   2. 设置 separate_eval=True 可分别评估锚点和穴位")
            print("   3. 调整 anchor_weight 和 acupoint_weight 控制综合评分权重")
            print("   4. 支持标准COCO AP指标: AP, AP.5, AP.75, AP(M), AP(L), AR等")
            print("   5. 使用OKS (Object Keypoint Similarity) 计算关键点相似度")
            print("   6. 支持不同sigma值：穴位使用更小的sigma要求更高精度")

            print("\n📋 配置文件示例:")
            print("   val_evaluator = dict(")
            print("       type='AcuRTMPoseMetric',")
            print("       ann_file='path/to/annotations.json',")
            print("       anchor_weight=0.4,")
            print("       acupoint_weight=0.6,")
            print("       separate_eval=True")
            print("   )")

            print("\n🔍 评估指标说明:")
            print("   - AP: Average Precision (平均精度)")
            print("   - AP.5: AP at IoU=0.5 (IoU阈值0.5时的AP)")
            print("   - AP.75: AP at IoU=0.75 (IoU阈值0.75时的AP)")
            print("   - AP(M): AP for medium objects (中等尺寸目标的AP)")
            print("   - AP(L): AP for large objects (大尺寸目标的AP)")
            print("   - AR: Average Recall (平均召回率)")

        else:
            print("\n❌ 测试失败")
            return False

    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
